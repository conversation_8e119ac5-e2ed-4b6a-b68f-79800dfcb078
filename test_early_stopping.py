#!/usr/bin/env python3
"""
Test script for early stopping functionality
"""

import os
import sys
import tempfile
import shutil
from omegaconf import OmegaConf

# Add the project root to the path
sys.path.insert(0, '/data/yzr/DUMP')

def test_early_stopping_config():
    """Test that early stopping configuration is properly loaded"""
    print("Testing early stopping configuration...")
    
    # Load the PPO trainer config
    config_path = "verl/trainer/config/ppo_trainer.yaml"
    config = OmegaConf.load(config_path)
    
    # Check if early stopping config exists
    assert 'early_stopping' in config.trainer, "Early stopping config not found in trainer config"
    
    early_stopping_config = config.trainer.early_stopping
    
    # Check all required fields
    required_fields = ['enable', 'patience', 'min_delta', 'metric', 'mode', 'restore_best_weights']
    for field in required_fields:
        assert field in early_stopping_config, f"Required field '{field}' not found in early stopping config"
    
    # Check default values
    assert early_stopping_config.enable == False, "Early stopping should be disabled by default"
    assert early_stopping_config.patience == 5, "Default patience should be 5"
    assert early_stopping_config.min_delta == 0.001, "Default min_delta should be 0.001"
    assert early_stopping_config.metric == "val/score/overall", "Default metric should be val/score/overall"
    assert early_stopping_config.mode == "max", "Default mode should be max"
    assert early_stopping_config.restore_best_weights == True, "Default restore_best_weights should be True"
    
    print("✓ Early stopping configuration test passed")

def test_early_stopping_initialization():
    """Test that early stopping is properly initialized in the trainer"""
    print("Testing early stopping initialization...")
    
    # Import the trainer class
    from verl.trainer.ppo.ray_trainer import RayPPOTrainer
    
    # Create a minimal config for testing
    config = OmegaConf.create({
        'trainer': {
            'early_stopping': {
                'enable': True,
                'patience': 3,
                'min_delta': 0.01,
                'metric': 'val/score/test',
                'mode': 'min',
                'restore_best_weights': False
            }
        }
    })
    
    # Create a mock trainer instance (we'll only test the initialization method)
    class MockTrainer:
        def __init__(self, config):
            self.config = config
            
        def _init_early_stopping(self):
            """Copy the method from RayPPOTrainer for testing"""
            early_stopping_config = self.config.trainer.get('early_stopping', {})
            self.early_stopping_enabled = early_stopping_config.get('enable', False)
            
            if self.early_stopping_enabled:
                self.early_stopping_patience = early_stopping_config.get('patience', 5)
                self.early_stopping_min_delta = early_stopping_config.get('min_delta', 0.001)
                self.early_stopping_metric = early_stopping_config.get('metric', 'val/score/overall')
                self.early_stopping_mode = early_stopping_config.get('mode', 'max')
                self.early_stopping_restore_best_weights = early_stopping_config.get('restore_best_weights', True)
                
                # Initialize tracking variables
                self.best_metric_value = float('-inf') if self.early_stopping_mode == 'max' else float('inf')
                self.best_metric_step = 0
                self.early_stopping_wait = 0
                self.early_stopping_stopped = False
                self.best_checkpoint_path = None
    
    trainer = MockTrainer(config)
    trainer._init_early_stopping()
    
    # Test initialization
    assert trainer.early_stopping_enabled == True, "Early stopping should be enabled"
    assert trainer.early_stopping_patience == 3, "Patience should be 3"
    assert trainer.early_stopping_min_delta == 0.01, "Min delta should be 0.01"
    assert trainer.early_stopping_metric == 'val/score/test', "Metric should be val/score/test"
    assert trainer.early_stopping_mode == 'min', "Mode should be min"
    assert trainer.early_stopping_restore_best_weights == False, "Restore best weights should be False"
    assert trainer.best_metric_value == float('inf'), "Best metric value should be inf for min mode"
    assert trainer.early_stopping_wait == 0, "Wait should be 0"
    assert trainer.early_stopping_stopped == False, "Stopped should be False"
    
    print("✓ Early stopping initialization test passed")

def test_early_stopping_logic():
    """Test the early stopping decision logic"""
    print("Testing early stopping logic...")
    
    # Create a mock trainer with early stopping logic
    class MockTrainer:
        def __init__(self):
            self.early_stopping_enabled = True
            self.early_stopping_patience = 2
            self.early_stopping_min_delta = 0.01
            self.early_stopping_metric = 'val/score/overall'
            self.early_stopping_mode = 'max'
            self.early_stopping_restore_best_weights = False
            
            self.best_metric_value = float('-inf')
            self.best_metric_step = 0
            self.early_stopping_wait = 0
            self.early_stopping_stopped = False
            self.best_checkpoint_path = None
            self.global_steps = 0
            
        def _check_early_stopping(self, val_metrics):
            """Copy the method from RayPPOTrainer for testing"""
            if not self.early_stopping_enabled or self.early_stopping_stopped:
                return False
                
            # Get the metric value to monitor
            metric_value = val_metrics.get(self.early_stopping_metric)
            if metric_value is None:
                return False
                
            # Check if this is an improvement
            is_improvement = False
            if self.early_stopping_mode == 'max':
                is_improvement = metric_value > self.best_metric_value + self.early_stopping_min_delta
            else:  # mode == 'min'
                is_improvement = metric_value < self.best_metric_value - self.early_stopping_min_delta
                
            if is_improvement:
                self.best_metric_value = metric_value
                self.best_metric_step = self.global_steps
                self.early_stopping_wait = 0
            else:
                self.early_stopping_wait += 1
                
            # Check if we should stop
            if self.early_stopping_wait >= self.early_stopping_patience:
                self.early_stopping_stopped = True
                return True
                
            return False
    
    trainer = MockTrainer()
    
    # Test improvement case
    trainer.global_steps = 1
    result = trainer._check_early_stopping({'val/score/overall': 0.5})
    assert result == False, "Should not stop on first improvement"
    assert trainer.best_metric_value == 0.5, "Best metric should be updated"
    assert trainer.early_stopping_wait == 0, "Wait should be reset"
    
    # Test another improvement
    trainer.global_steps = 2
    result = trainer._check_early_stopping({'val/score/overall': 0.6})
    assert result == False, "Should not stop on second improvement"
    assert trainer.best_metric_value == 0.6, "Best metric should be updated"
    assert trainer.early_stopping_wait == 0, "Wait should be reset"
    
    # Test no improvement (first time)
    trainer.global_steps = 3
    result = trainer._check_early_stopping({'val/score/overall': 0.59})
    assert result == False, "Should not stop on first no improvement"
    assert trainer.best_metric_value == 0.6, "Best metric should not change"
    assert trainer.early_stopping_wait == 1, "Wait should be 1"
    
    # Test no improvement (second time - should trigger early stopping)
    trainer.global_steps = 4
    result = trainer._check_early_stopping({'val/score/overall': 0.58})
    assert result == True, "Should stop after patience is exceeded"
    assert trainer.early_stopping_stopped == True, "Should be marked as stopped"
    
    print("✓ Early stopping logic test passed")

def main():
    """Run all tests"""
    print("Running early stopping tests...\n")
    
    try:
        test_early_stopping_config()
        test_early_stopping_initialization()
        test_early_stopping_logic()
        
        print("\n✅ All early stopping tests passed!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
