#!/usr/bin/env python3
"""
Test script to verify the Qwen2.5-1.5B influence function dimension fixes.
"""

import torch
import torch.nn as nn
import logging
import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, '/data/yzr/DUMP')

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_qwen_like_model():
    """Create a model that mimics Qwen2.5-1.5B architecture for testing."""
    
    class QwenLikeModel(nn.Module):
        def __init__(self):
            super().__init__()
            # Qwen2.5-1.5B dimensions
            self.hidden_size = 1536
            self.intermediate_size = 8960
            self.num_attention_heads = 12
            self.num_key_value_heads = 2
            self.head_dim = self.hidden_size // self.num_attention_heads  # 128
            
            # Embedding layer
            self.embed_tokens = nn.Embedding(1000, self.hidden_size)
            
            # Attention projections (simplified)
            self.q_proj = nn.Linear(self.hidden_size, self.hidden_size)  # 1536 -> 1536
            self.k_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim)  # 1536 -> 256
            self.v_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim)  # 1536 -> 256
            self.o_proj = nn.Linear(self.hidden_size, self.hidden_size)  # 1536 -> 1536
            
            # MLP layers
            self.gate_proj = nn.Linear(self.hidden_size, self.intermediate_size)  # 1536 -> 8960
            self.up_proj = nn.Linear(self.hidden_size, self.intermediate_size)    # 1536 -> 8960
            self.down_proj = nn.Linear(self.intermediate_size, self.hidden_size)  # 8960 -> 1536
            
            # Output layer
            self.lm_head = nn.Linear(self.hidden_size, 1000, bias=False)
            
        def forward(self, input_ids, attention_mask=None, **kwargs):
            batch_size, seq_len = input_ids.shape
            
            # Embedding
            hidden_states = self.embed_tokens(input_ids)  # [batch, seq_len, 1536]
            
            # Attention (simplified)
            q = self.q_proj(hidden_states)  # [batch, seq_len, 1536]
            k = self.k_proj(hidden_states)  # [batch, seq_len, 256]
            v = self.v_proj(hidden_states)  # [batch, seq_len, 256]
            
            # Simple attention computation (not actual multi-head)
            attn_output = self.o_proj(q)  # [batch, seq_len, 1536]
            
            # MLP
            gate = self.gate_proj(attn_output)  # [batch, seq_len, 8960]
            up = self.up_proj(attn_output)      # [batch, seq_len, 8960]
            mlp_output = self.down_proj(torch.relu(gate) * up)  # [batch, seq_len, 1536]
            
            # Output
            logits = self.lm_head(mlp_output)  # [batch, seq_len, 1000]
            
            return type('Output', (), {'logits': logits})()
    
    return QwenLikeModel()

def test_influence_function_fixes():
    """Test the influence function fixes with Qwen-like model."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    # Create model
    model = create_qwen_like_model().to(device)
    
    print("Model architecture:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            print(f"  {name}: {module.in_features} -> {module.out_features}")
    
    # Create test data with dimensions that might cause issues
    test_configs = [
        {
            'name': 'Small batch',
            'batch_size': 1,
            'seq_len': 512,
        },
        {
            'name': 'Medium batch',
            'batch_size': 2,
            'seq_len': 1024,
        }
    ]
    
    def loss_fn(outputs, batch):
        """Simple loss function for testing."""
        logits = outputs.logits
        # Create dummy targets
        targets = torch.randint(0, 1000, (logits.shape[0], logits.shape[1]), device=logits.device)
        return nn.CrossEntropyLoss()(logits.view(-1, logits.shape[-1]), targets.view(-1))
    
    for config in test_configs:
        print(f"\n=== Testing {config['name']} ===")
        
        # Create batch
        batch = {
            'input_ids': torch.randint(0, 1000, (config['batch_size'], config['seq_len'])).to(device),
            'attention_mask': torch.ones(config['batch_size'], config['seq_len']).to(device)
        }
        
        print(f"Batch shapes:")
        for key, value in batch.items():
            print(f"  {key}: {value.shape}")
        
        # Test basic model operations
        try:
            print("Testing basic model forward pass...")
            with torch.no_grad():
                outputs = model(**batch)
                print(f"✅ Forward pass successful, output shape: {outputs.logits.shape}")
                
            print("Testing loss computation...")
            loss = loss_fn(outputs, batch)
            print(f"✅ Loss computation successful: {loss.item():.4f}")
            
        except Exception as e:
            print(f"❌ Basic model operations failed: {e}")
            import traceback
            traceback.print_exc()
            continue
        
        # Test K-FAC influence function
        try:
            from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
            
            print("Testing K-FAC influence function calculator...")
            calculator = KFACInfluenceFunctionCalculator(
                model=model,
                regularization_lambda=1e-3,
                kfac_damping=1e-3,
                use_kfac=True,
                max_samples_per_batch=1
            )
            
            print("Testing K-FAC factor extraction...")
            calculator.extract_kfac_factors(batch, loss_fn)
            
            if calculator.kfac_factors:
                print(f"✅ Successfully extracted {len(calculator.kfac_factors)} K-FAC factors")
                for key, factor in calculator.kfac_factors.items():
                    print(f"  {key}: {factor.shape}")
            else:
                print("⚠️ No K-FAC factors extracted (may be expected due to dimension issues)")
            
            # Test influence score computation
            print("Testing influence score computation...")
            
            # Create smaller batches for influence computation
            train_sample = {
                'input_ids': batch['input_ids'][:1, :256],  # Smaller sample
                'attention_mask': batch['attention_mask'][:1, :256]
            }
            
            val_batch = {
                'input_ids': batch['input_ids'][:1, :256],  # Smaller validation batch
                'attention_mask': batch['attention_mask'][:1, :256]
            }
            
            start_time = time.time()
            influence_score = calculator.compute_influence_score(
                training_sample=train_sample,
                validation_batch=val_batch,
                loss_fn=loss_fn
            )
            
            print(f"✅ Influence score computation successful: {influence_score:.6f}")
            print(f"Test time: {time.time() - start_time:.2f}s")
            
        except Exception as e:
            print(f"❌ K-FAC influence function failed: {e}")
            import traceback
            traceback.print_exc()
            

if __name__ == "__main__":
    test_influence_function_fixes()
