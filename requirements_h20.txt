nvidia-cublas-cu12>=12.3.4.1
nvidia-cuda-cupti-cu12
nvidia-cuda-nvrtc-cu12
nvidia-cuda-runtime-cu12
nvidia-cudnn-cu12
nvidia-cufft-cu12
nvidia-curand-cu12
nvidia-cusolver-cu12
nvidia-nccl-cu12
nvidia-nvjitlink-cu12
nvidia-nvtx-cu12
nvidia-cusparse-cu12
msgspec
nbclient
tiktoken
smmap
tinycss2

accelerate==1.6.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.5
attrs==25.3.0
babel==2.17.0
beautifulsoup4==4.13.4
bleach==6.2.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
cmudict==1.0.32
codetiming==1.4.0
comm==0.2.2
contourpy==1.3.2
cycler==0.12.1
datasets==3.5.0
debugpy==1.8.14
decorator==5.2.1
defusedxml==0.7.1
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
docker-pycreds==0.4.0
einops==0.8.1
executing==2.2.0
fastapi==0.115.12
fastjsonschema==2.21.1
filelock==3.18.0
# flash-attn==2.6.3
fonttools==4.57.0
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2024.12.0
gguf==0.10.0
gitdb==4.0.12
gitpython==3.1.44
h11==0.14.0
httpcore==1.0.8
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.30.2
hydra-core==1.3.2
idna==3.10
importlib-metadata==8.6.1
importlib-resources==6.5.2
interegular==0.3.3
ipykernel==6.29.5
ipython==8.36.0
ipython-pygments-lexers==1.1.1
isoduration==20.11.0
jedi==0.19.2
jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
json5==0.12.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter-client==8.6.3
jupyter-core==5.7.2
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter-server==2.15.0
jupyter-server-terminals==0.5.3
jupyterlab==4.4.1
jupyterlab-pygments==0.3.0
jupyterlab-server==2.27.3
kiwisolver==1.4.8
lark==1.2.2
llvmlite==0.44.0
lm-format-enforcer==0.10.6
markdown-it-py==3.0.0
markupsafe==3.0.2
mathruler==0.1.0
matplotlib==3.10.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mistral-common==1.5.4
mistune==3.1.3
mpmath==1.3.0
msgpack==1.1.0
multidict==6.4.3
multiprocess==0.70.16
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
notebook-shim==0.2.4
numba==0.61.2
numpy==1.26.4
nvidia-ml-py==12.570.86
omegaconf==2.3.0
openai==1.74.0
opencv-python-headless==*********
orjson==3.10.16
outlines==0.0.46
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
partial-json-parser==*******.post5
pexpect==4.9.0
pillow==11.2.1
platformdirs==4.3.7
prometheus-client==0.21.1
prometheus-fastapi-instrumentator==7.1.0
prompt-toolkit==3.0.51
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
ptyprocess==0.7.0
pure-eval==0.2.3
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==19.0.1
pybind11==2.13.6
pycountry==24.6.1
pycparser==2.22
pydantic==2.11.3
pydantic-core==2.33.1
pygments==2.19.1
pylatexenc==2.10
pynndescent==0.5.13
pyparsing==3.2.3
pyphen==0.17.2
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-json-logger==3.3.0
pytz==2025.2
pyyaml==6.0.2
pyzmq==26.4.0
ray==2.44.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==14.0.0
rpds-py==0.24.0
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
send2trash==1.8.3
sentence-transformers==4.1.0
sentencepiece==0.2.0
sentry-sdk==2.26.0
setproctitle==1.3.5
setuptools==78.1.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
stack-data==0.6.3
starlette==0.46.2
sympy==1.13.3
tensordict==0.5.0
terminado==0.18.1
textstat==0.7.5

threadpoolctl==3.6.0
tokenizers==0.21.1
torch==2.4.0
torchvision==0.19.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.47.1
triton==3.0.0
types-python-dateutil==2.9.0.20241206
typing-extensions==4.13.2
typing-inspection==0.4.0
tzdata==2025.2
umap-learn==0.5.7
uri-template==1.3.0
urllib3==2.4.0
uvicorn==0.34.1
uvloop==0.21.0
vllm==0.6.3
wandb==0.19.9
watchfiles==1.0.5
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
xformers==0.0.27.post2
xxhash==3.5.0
yarl==1.19.0
zipp==3.21.0