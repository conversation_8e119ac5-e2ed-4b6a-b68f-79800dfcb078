# Early Stopping 功能使用指南

## 概述

早停（Early Stopping）功能可以在验证指标持续一段时间没有改善，甚至持续下降时，自动中断训练。这有助于：

1. **防止过拟合**：在模型开始过拟合之前停止训练
2. **节省计算资源**：避免无效的训练时间
3. **自动化训练**：无需手动监控训练过程

## 配置参数

在训练配置文件中，早停功能通过 `trainer.early_stopping` 部分进行配置：

```yaml
trainer:
  early_stopping:
    enable: False  # 是否启用早停功能
    patience: 5  # 等待改善的验证步数
    min_delta: 0.001  # 认定为改善的最小变化量
    metric: "val/score/overall"  # 监控的指标名称
    mode: "max"  # "max" 表示指标越大越好，"min" 表示指标越小越好
    restore_best_weights: True  # 是否在早停时恢复最佳模型权重
```

### 参数详解

- **enable**: 布尔值，设置为 `True` 启用早停功能
- **patience**: 整数，表示在多少个验证步骤内没有改善就触发早停
- **min_delta**: 浮点数，指标改善的最小阈值，小于此值的变化不被认为是改善
- **metric**: 字符串，要监控的验证指标名称，常见的有：
  - `val/score/overall`: 总体验证分数
  - `val/score/{data_source}`: 特定数据源的验证分数
- **mode**: 字符串，指标优化方向：
  - `"max"`: 指标越大越好（如准确率、奖励分数）
  - `"min"`: 指标越小越好（如损失、错误率）
- **restore_best_weights**: 布尔值，是否在早停时恢复到最佳性能时的模型权重

## 使用示例

### 1. 基本使用

启用早停功能，监控总体验证分数：

```yaml
trainer:
  early_stopping:
    enable: True
    patience: 5
    min_delta: 0.001
    metric: "val/score/overall"
    mode: "max"
    restore_best_weights: True
```

### 2. 监控特定数据源

监控特定数据源的验证分数：

```yaml
trainer:
  early_stopping:
    enable: True
    patience: 3
    min_delta: 0.01
    metric: "val/score/math_cluster_1"
    mode: "max"
    restore_best_weights: True
```

### 3. 监控损失函数

监控验证损失（越小越好）：

```yaml
trainer:
  early_stopping:
    enable: True
    patience: 10
    min_delta: 0.0001
    metric: "val/loss"
    mode: "min"
    restore_best_weights: False
```

### 4. 命令行覆盖配置

可以通过命令行参数覆盖配置：

```bash
python -m verl.trainer.main_ppo \
    trainer.early_stopping.enable=True \
    trainer.early_stopping.patience=3 \
    trainer.early_stopping.metric="val/score/overall" \
    trainer.early_stopping.mode="max" \
    # ... 其他参数
```

## 工作原理

1. **初始化**：训练开始时，早停功能初始化相关变量
2. **验证监控**：每次验证时，检查指定的指标值
3. **改善判断**：
   - 对于 `mode="max"`：当前值 > 最佳值 + min_delta 时认为有改善
   - 对于 `mode="min"`：当前值 < 最佳值 - min_delta 时认为有改善
4. **等待计数**：
   - 有改善时：重置等待计数器，更新最佳值
   - 无改善时：等待计数器 +1
5. **早停触发**：等待计数器达到 patience 时触发早停
6. **权重恢复**：如果启用了 `restore_best_weights`，恢复到最佳性能时的模型权重

## 日志输出

早停功能会产生以下日志输出：

```
Early stopping: New best val/score/overall: 0.856000 (previous: 0.850000)
Early stopping: No improvement in val/score/overall: 0.854000 (best: 0.856000, wait: 1/5)
Early stopping triggered! No improvement in val/score/overall for 5 validation steps.
Best val/score/overall: 0.856000 at step 120
Restoring best model weights from step 120
```

## 检查点保存

早停功能会自动保存和恢复相关状态：

- **保存**：在每次保存检查点时，同时保存早停状态
- **恢复**：从检查点恢复时，自动恢复早停状态
- **最佳权重**：如果启用了权重恢复，会单独保存最佳性能时的模型权重

## 注意事项

1. **验证频率**：早停功能依赖于验证，确保设置了合适的 `trainer.test_freq`
2. **指标可用性**：确保监控的指标在验证过程中确实会被计算和记录
3. **存储空间**：启用权重恢复会额外占用存储空间保存最佳模型
4. **patience 设置**：patience 过小可能导致过早停止，过大可能失去早停的意义
5. **min_delta 设置**：根据指标的数值范围合理设置 min_delta

## 与其他功能的兼容性

早停功能与以下功能完全兼容：

- ✅ 课程学习（Curriculum Learning）
- ✅ 影响函数（Influence Functions）
- ✅ 检查点保存和恢复
- ✅ 分布式训练
- ✅ WandB 日志记录

## 故障排除

### 常见问题

1. **指标未找到**：
   ```
   Warning: Early stopping metric 'val/score/xxx' not found in validation metrics
   ```
   解决方案：检查指标名称是否正确，确保验证过程中会计算该指标

2. **早停未触发**：
   - 检查 `enable` 是否设置为 `True`
   - 检查 `test_freq` 是否设置正确
   - 检查 patience 和 min_delta 设置是否合理

3. **权重恢复失败**：
   ```
   Warning: Failed to restore best checkpoint: xxx
   ```
   解决方案：检查存储空间和权限，确保检查点目录可写

通过合理配置早停功能，可以显著提高训练效率并获得更好的模型性能。
