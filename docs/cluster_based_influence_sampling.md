# Cluster-Based Influence Function Sampling

## Overview

This feature implements efficient cluster-based sampling for influence function computation, significantly reducing computation time while maintaining cluster representation in curriculum learning.

## Problem

Previously, influence functions were computed for every sample in each training batch, which was computationally expensive and time-consuming. For large batches and complex models, this could become a bottleneck in training.

## Solution

The new cluster-based sampling approach:

1. **Groups samples by cluster**: Uses existing cluster information in the dataset
2. **Samples within clusters**: Selects every N-th sample within each cluster
3. **Maintains representation**: Ensures all clusters are represented in influence computation
4. **Reduces computation**: Significantly decreases the number of influence function calculations

## Configuration

Add these parameters to your training configuration:

```yaml
data:
  # Enable cluster-based sampling
  influence_cluster_sampling_interval: 5  # Sample every 5th sample per cluster
  influence_cluster_key: "data_source"     # Column containing cluster information
```

### Parameters

- **`influence_cluster_sampling_interval`** (int, default: 1)
  - Sampling interval within each cluster
  - `1` = compute for all samples (original behavior)
  - `5` = compute for every 5th sample per cluster
  - Higher values = more efficiency, less computation

- **`influence_cluster_key`** (str, default: "data_source")
  - Column name containing cluster information
  - Common values: "data_source", "cluster", "accuracy_bin"

## Efficiency Gains

| Interval | Samples Computed | Efficiency Gain |
|----------|------------------|-----------------|
| 1        | 100%            | 0% (baseline)   |
| 3        | ~33%            | ~67% reduction  |
| 5        | ~20%            | ~80% reduction  |
| 10       | ~10%            | ~90% reduction  |

## Usage Examples

### Basic Usage

```yaml
data:
  enable_curriculum_learning: true
  use_influence_functions: true
  influence_cluster_sampling_interval: 5
  influence_cluster_key: "data_source"
```

### Different Scenarios

```yaml
# High efficiency (recommended for large models)
influence_cluster_sampling_interval: 10

# Balanced approach (recommended for most cases)
influence_cluster_sampling_interval: 5

# Conservative approach (for critical applications)
influence_cluster_sampling_interval: 3

# Original behavior (no sampling)
influence_cluster_sampling_interval: 1
```

## Data Requirements

Your dataset must contain cluster information. Common cluster columns:

- `data_source`: Generated by curriculum learning
- `cluster`: Generated by embedding clustering
- `accuracy_bin`: Generated by difficulty-based clustering

Example dataset structure:
```
prompt | response | data_source | cluster
-------|----------|-------------|--------
"..."  | "..."    | "easy"      | 0
"..."  | "..."    | "medium"    | 1
"..."  | "..."    | "hard"      | 2
```

## Implementation Details

### Sampling Algorithm

1. **Group by cluster**: Samples are grouped by their cluster ID
2. **Sort within cluster**: Indices are sorted for consistent sampling
3. **Apply interval**: Select every N-th sample using `indices[::interval]`
4. **Combine results**: Selected indices from all clusters are combined

### Fallback Behavior

- If no cluster information is found, falls back to uniform sampling across the batch
- Non-selected samples receive a default influence score of 0.0
- Error handling ensures training continues even if sampling fails

### Memory Management

- Maintains the same memory management patterns as original implementation
- No additional memory overhead for cluster-based sampling
- Compatible with existing FSDP and Ulysses parallelism

## Testing

Run the test script to verify functionality:

```bash
python test_cluster_influence_sampling.py
```

This will test:
- Cluster-based sample selection
- Different sampling intervals
- Efficiency comparisons
- Edge cases and error handling

## Performance Impact

### Computation Time Reduction

- **Interval 5**: ~80% reduction in influence computation time
- **Interval 10**: ~90% reduction in influence computation time

### Training Quality

- Maintains cluster representation in curriculum learning
- Minimal impact on convergence when using appropriate intervals
- Recommended to start with interval 5 and adjust based on results

## Best Practices

1. **Start conservative**: Begin with interval 3-5 for initial experiments
2. **Monitor convergence**: Watch training metrics to ensure quality is maintained
3. **Adjust based on model size**: Larger models may benefit from higher intervals
4. **Consider cluster balance**: Ensure clusters have sufficient samples for meaningful sampling

## Troubleshooting

### Common Issues

1. **No cluster information found**
   - Ensure your dataset has the specified cluster column
   - Check the `influence_cluster_key` parameter
   - Verify data preprocessing includes clustering

2. **Unexpected sampling behavior**
   - Run the test script to verify implementation
   - Check cluster distribution in your dataset
   - Ensure cluster IDs are consistent

3. **Performance not improved**
   - Verify interval > 1
   - Check that influence functions are actually enabled
   - Monitor GPU utilization during training

### Debug Information

The implementation provides detailed logging:
- Number of samples selected per cluster
- Total efficiency gain
- Fallback behavior when cluster info is missing

## Future Enhancements

Potential improvements for future versions:
- Adaptive sampling based on cluster importance
- Dynamic interval adjustment during training
- Integration with other sampling strategies
- Support for hierarchical clustering
