#!/usr/bin/env python3
"""
Test script to simulate the actual training scenario that was failing.
This tests the specific code path that was causing the Qwen3 error.
"""

import sys
import traceback
import tempfile
import json
import os


def create_mock_qwen3_model_dir():
    """Create a temporary directory with Qwen3 model files"""
    temp_dir = tempfile.mkdtemp()
    
    # Create config.json
    qwen3_config = {
        "model_type": "qwen3",
        "architectures": ["Qwen3ForCausalLM"],
        "vocab_size": 151936,
        "hidden_size": 4096,
        "intermediate_size": 12288,
        "num_hidden_layers": 36,
        "num_attention_heads": 32,
        "num_key_value_heads": 8,
        "head_dim": 128,
        "attention_bias": False,
        "hidden_act": "silu",
        "max_position_embeddings": 40960,
        "rope_theta": 1000000,
        "rms_norm_eps": 1e-06,
        "use_cache": True,
        "tie_word_embeddings": False,
        "torch_dtype": "bfloat16",
        "transformers_version": "4.47.1"
    }
    
    config_path = os.path.join(temp_dir, "config.json")
    with open(config_path, 'w') as f:
        json.dump(qwen3_config, f, indent=2)
    
    # Create generation_config.json
    generation_config = {
        "do_sample": True,
        "eos_token_id": 151645,
        "pad_token_id": 151643,
        "repetition_penalty": 1.05,
        "temperature": 0.7,
        "top_k": 20,
        "top_p": 0.8,
        "transformers_version": "4.47.1"
    }
    
    gen_config_path = os.path.join(temp_dir, "generation_config.json")
    with open(gen_config_path, 'w') as f:
        json.dump(generation_config, f, indent=2)
    
    print(f"✓ Created mock Qwen3 model directory: {temp_dir}")
    return temp_dir


def test_autoconfig_loading():
    """Test the specific AutoConfig.from_pretrained call that was failing"""
    print("Testing AutoConfig.from_pretrained with Qwen3...")
    
    try:
        # Import verl first to initialize Qwen3 support
        import verl
        print("✓ VERL imported successfully")
        
        # Import AutoConfig
        from transformers import AutoConfig
        
        # Create mock model directory
        model_dir = create_mock_qwen3_model_dir()
        
        try:
            # This is the exact call that was failing in fsdp_workers.py line 171
            actor_model_config = AutoConfig.from_pretrained(model_dir, trust_remote_code=False)
            
            print(f"✓ Successfully loaded config: {type(actor_model_config).__name__}")
            print(f"  - model_type: {actor_model_config.model_type}")
            print(f"  - architectures: {getattr(actor_model_config, 'architectures', 'not set')}")
            print(f"  - head_dim: {getattr(actor_model_config, 'head_dim', 'not set')}")
            print(f"  - attention_bias: {getattr(actor_model_config, 'attention_bias', 'not set')}")
            
            return True
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(model_dir)
            
    except Exception as e:
        print(f"✗ AutoConfig loading failed: {e}")
        traceback.print_exc()
        return False


def test_generation_config_loading():
    """Test generation config loading"""
    print("\nTesting generation config loading...")
    
    try:
        from verl.utils.hf_tokenizer import get_generation_config
        
        # Create mock model directory
        model_dir = create_mock_qwen3_model_dir()
        
        try:
            generation_config = get_generation_config(model_dir, trust_remote_code=False)
            print(f"✓ Successfully loaded generation config: {type(generation_config)}")
            
            return True
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(model_dir)
            
    except Exception as e:
        print(f"✗ Generation config loading failed: {e}")
        traceback.print_exc()
        return False


def test_model_registry_with_qwen3():
    """Test model registry with Qwen3"""
    print("\nTesting model registry with Qwen3...")
    
    try:
        from verl.models.registry import ModelRegistry
        
        # Test if Qwen3ForCausalLM is supported
        supported_archs = ModelRegistry.get_supported_archs()
        if "Qwen3ForCausalLM" in supported_archs:
            print("✓ Qwen3ForCausalLM is in supported architectures")
        else:
            print("✗ Qwen3ForCausalLM is not in supported architectures")
            return False
        
        # Test loading model class (this might fail due to missing megatron, but that's OK)
        try:
            actor_cls = ModelRegistry.load_model_cls("Qwen3ForCausalLM", value=False)
            if actor_cls is not None:
                print(f"✓ Successfully loaded actor model class: {actor_cls.__name__}")
            else:
                print("⚠️  Actor model class is None (expected if megatron not installed)")
        except Exception as e:
            print(f"⚠️  Model class loading failed (expected if megatron not installed): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model registry test failed: {e}")
        traceback.print_exc()
        return False


def test_fsdp_worker_imports():
    """Test that FSDP worker can import without errors"""
    print("\nTesting FSDP worker imports...")
    
    try:
        # This should trigger our Qwen3 initialization
        from verl.workers.fsdp_workers import FSDPWorker
        print("✓ Successfully imported FSDPWorker")
        
        return True
        
    except Exception as e:
        print(f"✗ FSDP worker import failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests that simulate the training scenario"""
    print("=" * 60)
    print("Qwen3 Training Scenario Test Suite")
    print("=" * 60)
    
    tests = [
        test_autoconfig_loading,
        test_generation_config_loading,
        test_model_registry_with_qwen3,
        test_fsdp_worker_imports,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 60)
    print("Training Scenario Test Results")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All training scenario tests passed! Qwen3 should work in actual training.")
        return 0
    else:
        print("❌ Some tests failed. The training scenario may still have issues.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
