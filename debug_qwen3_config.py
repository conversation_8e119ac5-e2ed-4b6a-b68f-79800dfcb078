#!/usr/bin/env python3
"""
Debug script to check Qwen3 configuration and model dimensions
"""

import sys
import tempfile
import json
import os
import shutil


def debug_qwen3_config():
    """Debug Qwen3 configuration loading and model dimensions"""
    print("🔍 Debugging Qwen3 Configuration")
    print("=" * 50)
    
    # Import verl to initialize Qwen3 support
    import verl
    from transformers import AutoConfig
    
    # Create mock Qwen3-0.6B config file with correct parameters
    qwen3_config = {
        "model_type": "qwen3",
        "architectures": ["Qwen3ForCausalLM"],
        "vocab_size": 151936,
        "hidden_size": 1024,
        "intermediate_size": 3072,
        "num_hidden_layers": 28,
        "num_attention_heads": 16,
        "num_key_value_heads": 8,
        "head_dim": 128,
        "attention_bias": False,
        "hidden_act": "silu",
        "max_position_embeddings": 40960,
        "rope_theta": 1000000,
        "rms_norm_eps": 1e-06,
        "use_cache": True,
        "tie_word_embeddings": True,
        "max_window_layers": 28,
        "use_sliding_window": False
    }
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        config_path = os.path.join(temp_dir, "config.json")
        with open(config_path, 'w') as f:
            json.dump(qwen3_config, f, indent=2)
        
        print("📋 Loading Qwen3 config...")
        config = AutoConfig.from_pretrained(temp_dir)
        
        print(f"✓ Config type: {type(config).__name__}")
        print(f"✓ Model type: {config.model_type}")
        print()
        
        print("📊 Configuration Parameters:")
        print(f"  - vocab_size: {config.vocab_size}")
        print(f"  - hidden_size: {config.hidden_size}")
        print(f"  - intermediate_size: {config.intermediate_size}")
        print(f"  - num_hidden_layers: {config.num_hidden_layers}")
        print(f"  - num_attention_heads: {config.num_attention_heads}")
        print(f"  - num_key_value_heads: {config.num_key_value_heads}")
        print(f"  - head_dim: {getattr(config, 'head_dim', 'NOT SET')}")
        print(f"  - attention_bias: {getattr(config, 'attention_bias', 'NOT SET')}")
        print()
        
        # Calculate expected dimensions
        print("🧮 Expected Weight Dimensions:")
        head_dim = getattr(config, 'head_dim', config.hidden_size // config.num_attention_heads)
        print(f"  - Effective head_dim: {head_dim}")
        
        q_proj_shape = (config.num_attention_heads * head_dim, config.hidden_size)
        k_proj_shape = (config.num_key_value_heads * head_dim, config.hidden_size)
        v_proj_shape = (config.num_key_value_heads * head_dim, config.hidden_size)
        o_proj_shape = (config.hidden_size, config.num_attention_heads * head_dim)
        
        print(f"  - q_proj.weight: {q_proj_shape}")
        print(f"  - k_proj.weight: {k_proj_shape}")
        print(f"  - v_proj.weight: {v_proj_shape}")
        print(f"  - o_proj.weight: {o_proj_shape}")
        print()
        
        # Check if dimensions are consistent
        print("✅ Dimension Consistency Check:")
        if head_dim * config.num_attention_heads == config.hidden_size:
            print(f"  ✓ head_dim * num_attention_heads = {head_dim * config.num_attention_heads} == hidden_size ({config.hidden_size})")
        else:
            print(f"  ⚠️  head_dim * num_attention_heads = {head_dim * config.num_attention_heads} != hidden_size ({config.hidden_size})")
            print(f"      This is OK for Qwen3 architecture")
        
        # Test model creation (if possible)
        print("\n🏗️  Testing Model Creation:")
        try:
            # Try to create a simple attention layer to see the actual dimensions
            from verl.models.qwen3.megatron.layers.parallel_attention import ParallelQwen3Attention
            from megatron.core import ModelParallelConfig
            
            # Create a minimal megatron config
            megatron_config = ModelParallelConfig()
            
            print("  - Creating ParallelQwen3Attention...")
            attention = ParallelQwen3Attention(config, megatron_config)
            
            print(f"  ✓ Attention layer created successfully")
            print(f"    - head_dim: {attention.head_dim}")
            print(f"    - num_heads: {attention.num_heads}")
            print(f"    - num_key_value_heads: {attention.num_key_value_heads}")
            print(f"    - q_size: {attention.q_size}")
            print(f"    - k_size: {attention.k_size}")
            print(f"    - v_size: {attention.v_size}")
            
        except Exception as e:
            print(f"  ✗ Model creation failed: {e}")
            print("    (This is expected if megatron is not properly initialized)")
        
        return True
        
    except Exception as e:
        print(f"✗ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir)


def main():
    """Run the debug script"""
    success = debug_qwen3_config()
    
    if success:
        print("\n🎉 Debug completed successfully!")
        return 0
    else:
        print("\n❌ Debug failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
