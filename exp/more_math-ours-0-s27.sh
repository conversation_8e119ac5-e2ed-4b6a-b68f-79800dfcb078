#!/bin/bash
set -x
MODEL_PATH=/data/oss_bucket_0/Users/<USER>//Llama-3.2-1B-Instruct
sp_size=2 # 🌴🌴🌴🌴 并行度,几张卡就设为几
export VLLM_ATTENTION_BACKEND=XFORMERS
# export CUDA_VISIBLE_DEVICES=1,2
export NCCL_TIMEOUT=1800000

SEEDS=(27) 

max_prompt_length=$((1024 * 1))
max_response_length=$((1024 * 3))

use_dynamic_bsz=True
# actor_ppo_max_token_len=8500
# infer_ppo_max_token_len=8500
# 三卡8500

actor_ppo_max_token_len=9500
infer_ppo_max_token_len=9500
offload=False
gen_tp=1

train_batch_size=8

model_name=${MODEL_PATH##*/} 
datasets=("math500" "aime2024" "gsm8k" "math")
data_path=examples/data

for seed in "${SEEDS[@]}"; do
    echo "Running with seed: $seed"
    # 对于每一对train_file和test_file，运行下面的脚本
    for i in $(seq 0 0); do
        dataset=${datasets[$i]}
        train_file=$data_path/$dataset/train.parquet
        val_file=$data_path/$dataset/val.parquet
        test_file=$data_path/$dataset/test.parquet
        exp_name=$dataset-if-more-math-s$seed

        for use_curriculum_learning in True; do
                echo "Running with curriculum learning: $use_curriculum_learning"
                python3 -m verl.trainer.main_ppo \
                    algorithm.adv_estimator=grpo \
                    data.train_files="$train_file" \
                    data.val_files="$val_file" \
                    +data.test_files="$test_file" \
                    data.train_batch_size=${train_batch_size} \
                    data.enable_curriculum_learning=${use_curriculum_learning} \
                    +data.use_influence_functions=True \
                    +data.influence_use_kfac=True\
                    +data.influence_regularization_lambda=1e-3\
                    +data.seed=${seed} \
                    ++data.shuffle=True \
                    +data.data_source_key=accuracy_bin \
                    data.max_prompt_length="${max_prompt_length}" \
                    data.max_response_length="${max_response_length}" \
                    actor_rollout_ref.actor.use_dynamic_bsz=${use_dynamic_bsz} \
                    actor_rollout_ref.ref.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
                    actor_rollout_ref.rollout.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
                    actor_rollout_ref.actor.ppo_max_token_len_per_gpu=${actor_ppo_max_token_len} \
                    actor_rollout_ref.ref.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
                    actor_rollout_ref.rollout.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
                    actor_rollout_ref.actor.ulysses_sequence_parallel_size=${gen_tp} \
                    actor_rollout_ref.ref.ulysses_sequence_parallel_size=${gen_tp} \
                    actor_rollout_ref.model.path=$MODEL_PATH \
                    actor_rollout_ref.actor.optim.lr=1e-6 \
                    actor_rollout_ref.model.use_remove_padding=True \
                    actor_rollout_ref.actor.ppo_mini_batch_size=32 \
                    actor_rollout_ref.actor.use_kl_loss=True \
                    actor_rollout_ref.actor.kl_loss_coef=0.001 \
                    actor_rollout_ref.actor.kl_loss_type=low_var_kl \
                    actor_rollout_ref.model.enable_gradient_checkpointing=True \
                    actor_rollout_ref.actor.fsdp_config.param_offload=${offload} \
                    actor_rollout_ref.actor.fsdp_config.optimizer_offload=${offload} \
                    actor_rollout_ref.rollout.name=vllm \
                    actor_rollout_ref.rollout.gpu_memory_utilization=0.6 \
                    actor_rollout_ref.rollout.n=6 \
                    actor_rollout_ref.rollout.enforce_eager=True \
                    actor_rollout_ref.rollout.free_cache_engine=False \
                    actor_rollout_ref.rollout.enable_chunked_prefill=True \
                    actor_rollout_ref.rollout.tensor_model_parallel_size=${gen_tp} \
                    actor_rollout_ref.ref.fsdp_config.param_offload=${offload} \
                    algorithm.kl_ctrl.kl_coef=0.001 \
                    trainer.critic_warmup=0 \
                    trainer.logger=['wandb','console'] \
                    trainer.project_name='GRPO_combined_logic' \
                    +data.method="ours" \
                    trainer.experiment_name=${exp_name}-${seed}-${use_curriculum_learning}-${model_name}-GRPO \
                    trainer.n_gpus_per_node=${sp_size} \
                    trainer.nnodes=1 \
                    trainer.default_local_dir=./ckpt/${exp_name}-GRPO-${use_curriculum_learning}-${model_name} \
                    trainer.default_hdfs_dir=null \
                    trainer.remove_previous_ckpt_in_save=True \
                    trainer.hf_account=Tim-Saijun \
                    trainer.cleanup_after_upload=True \
                    trainer.save_freq=200 \
                    trainer.test_freq=10 \
                    trainer.total_epochs=3 $@ 2>&1 | tee ./ckpt/${exp_name}-GRPO-${use_curriculum_learning}-${model_name}.log
        done
    done
done