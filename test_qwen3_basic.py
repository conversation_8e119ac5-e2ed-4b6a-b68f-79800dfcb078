#!/usr/bin/env python3
"""
Basic test script to verify Qwen3 model integration
"""

import sys
import traceback

def test_registry():
    """Test if Qwen3 is properly registered"""
    print("Testing model registry...")
    try:
        from verl.models.registry import ModelRegistry
        supported_archs = ModelRegistry.get_supported_archs()
        print(f"Supported architectures: {supported_archs}")
        
        if 'Qwen3ForCausalLM' in supported_archs:
            print("✓ Qwen3ForCausalLM is registered")
            return True
        else:
            print("✗ Qwen3ForCausalLM is not registered")
            return False
    except Exception as e:
        print(f"✗ Registry test failed: {e}")
        traceback.print_exc()
        return False

def test_weight_loader():
    """Test if Qwen3 weight loader is available"""
    print("\nTesting weight loader...")
    try:
        from verl.models.weight_loader_registry import get_weight_loader
        loader = get_weight_loader('Qwen3ForCausalLM')
        print(f"✓ Qwen3 weight loader found: {loader.__name__}")
        return True
    except Exception as e:
        print(f"✗ Weight loader test failed: {e}")
        # This is expected to fail due to missing megatron dependency
        print("  (This is expected if megatron is not installed)")
        return True  # Don't fail the test for this

def test_transformers_adapter():
    """Test if Qwen3 transformers adapter is available"""
    print("\nTesting transformers adapter...")
    try:
        from verl.models.transformers import qwen3
        print("✓ Qwen3 transformers adapter imported successfully")
        
        # Test if the flash attention function exists
        if hasattr(qwen3, 'qwen3_flash_attn_forward'):
            print("✓ qwen3_flash_attn_forward function found")
            return True
        else:
            print("✗ qwen3_flash_attn_forward function not found")
            return False
    except Exception as e:
        print(f"✗ Transformers adapter test failed: {e}")
        traceback.print_exc()
        return False

def test_config_compatibility():
    """Test if Qwen3 config is compatible with Qwen2"""
    print("\nTesting config compatibility...")
    try:
        from transformers import Qwen2Config
        
        # Test creating a config with Qwen3-like parameters
        config = Qwen2Config(
            vocab_size=151936,
            hidden_size=4096,
            intermediate_size=12288,
            num_hidden_layers=36,
            num_attention_heads=32,
            num_key_value_heads=8,
            hidden_act="silu",
            max_position_embeddings=40960,
            rope_theta=1000000,
            rms_norm_eps=1e-06,
            use_cache=True,
            tie_word_embeddings=False,
            # Qwen3 specific fields (will be ignored if not supported)
            head_dim=128,
            attention_bias=False,
        )
        
        print("✓ Qwen3-compatible config created successfully")
        print(f"  - vocab_size: {config.vocab_size}")
        print(f"  - hidden_size: {config.hidden_size}")
        print(f"  - num_hidden_layers: {config.num_hidden_layers}")
        print(f"  - num_attention_heads: {config.num_attention_heads}")
        print(f"  - head_dim: {getattr(config, 'head_dim', 'not set')}")
        print(f"  - attention_bias: {getattr(config, 'attention_bias', 'not set')}")
        
        return True
    except Exception as e:
        print(f"✗ Config compatibility test failed: {e}")
        traceback.print_exc()
        return False

def test_rmpad_models():
    """Test if RmPad models are properly defined"""
    print("\nTesting RmPad models...")
    try:
        from verl.models.registry import _REOVEPAD_MODELS
        
        if 'qwen3' in _REOVEPAD_MODELS:
            print("✓ qwen3 is in RmPad models registry")
            print(f"  Config class: {_REOVEPAD_MODELS['qwen3']}")
            return True
        else:
            print("✗ qwen3 is not in RmPad models registry")
            return False
    except Exception as e:
        print(f"✗ RmPad models test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Qwen3 Integration Test Suite")
    print("=" * 60)
    
    tests = [
        test_registry,
        test_weight_loader,
        test_transformers_adapter,
        test_config_compatibility,
        test_rmpad_models,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Qwen3 integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
