#!/usr/bin/env python3
"""
Debug script to reproduce the exact error scenario.
"""

import torch
import torch.nn as nn
import logging

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_exact_error_scenario():
    """Test with the exact scenario from the error logs."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    # Create a model that exactly matches the error scenario
    # From debug info: Linear(1536, 1536), Linear(1536, 256), Linear(1536, 256)
    # Input shape: [1, 1024, 1536]
    class ExactErrorModel(nn.Module):
        def __init__(self):
            super().__init__()
            # These are the exact modules from the error log
            self.layer1 = nn.Linear(1536, 1536)  # Linear(1536, 1536)
            self.layer2 = nn.Linear(1536, 256)   # Linear(1536, 256)  
            self.layer3 = nn.Linear(1536, 256)   # Linear(1536, 256)
            
        def forward(self, input_ids, attention_mask=None, **kwargs):
            # Create the exact input shape from the error: [1, 1024, 1536]
            x = torch.randn(1, 1024, 1536, device=input_ids.device)
            
            # Apply the transformations
            x1 = self.layer1(x)  # [1, 1024, 1536] -> [1, 1024, 1536]
            x2 = self.layer2(x)  # [1, 1024, 1536] -> [1, 1024, 256]
            x3 = self.layer3(x)  # [1, 1024, 1536] -> [1, 1024, 256]
            
            # Combine outputs somehow (this might be where the dimension mismatch occurs)
            # Let's try different combinations that might cause the 2048 vs 1024 error
            
            # Option 1: Concatenate along last dimension
            # x1: [1, 1024, 1536], x2: [1, 1024, 256], x3: [1, 1024, 256]
            # Total: 1536 + 256 + 256 = 2048 (this might be the source of 2048!)
            combined = torch.cat([x1, x2, x3], dim=-1)  # [1, 1024, 2048]
            
            # Now if we try to do some operation that expects 1024 instead of 2048...
            # This could be the source of the error!
            
            return type('Output', (), {'logits': combined})()
    
    model = ExactErrorModel().to(device)
    
    print("Model architecture:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            print(f"  {name}: {module.in_features} -> {module.out_features}")
    
    # Create test data
    batch = {
        'input_ids': torch.randint(0, 1000, (1, 512)).to(device),
        'attention_mask': torch.ones(1, 512).to(device)
    }
    
    def loss_fn(model_output, batch_data):
        logits = model_output.logits  # [1, 1024, 2048]
        # Create labels that match
        labels = torch.randint(0, 2048, (logits.shape[0], logits.shape[1])).to(logits.device)
        return torch.nn.functional.cross_entropy(
            logits.view(-1, logits.size(-1)),
            labels.view(-1)
        )
    
    print("\n=== Testing Exact Error Scenario ===")
    
    # First, let's see what happens with a forward pass
    try:
        print("Testing forward pass...")
        output = model(**batch)
        print(f"Forward pass successful, output shape: {output.logits.shape}")
        
        print("Testing loss computation...")
        loss = loss_fn(output, batch)
        print(f"Loss computation successful: {loss.item()}")
        
        print("Testing backward pass...")
        loss.backward()
        print("Backward pass successful")
        
    except Exception as e:
        print(f"❌ Basic model operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Now test K-FAC
    try:
        from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
        
        calculator = KFACInfluenceFunctionCalculator(
            model=model,
            regularization_lambda=1e-3,
            kfac_damping=1e-3,
            use_kfac=True
        )
        
        print("\nTesting K-FAC factor extraction...")
        calculator.extract_kfac_factors(batch, loss_fn)
        
        if calculator.kfac_factors:
            print(f"✅ Successfully extracted {len(calculator.kfac_factors)} K-FAC factors")
            for key, factor in calculator.kfac_factors.items():
                print(f"  {key}: {factor.shape}")
        else:
            print("❌ No K-FAC factors extracted")
            
        return True
        
    except Exception as e:
        print(f"❌ K-FAC test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_matrix_multiplication_directly():
    """Test matrix multiplication operations directly to find the issue."""
    
    print("\n=== Testing Matrix Multiplication Directly ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Test the specific dimensions that are causing issues
    test_cases = [
        {
            'name': 'Case 1: 2048 vs 1024',
            'tensor_a_shape': (1024, 2048),
            'tensor_b_shape': (1024, 1024),
        },
        {
            'name': 'Case 2: 8192 vs 4096', 
            'tensor_a_shape': (4096, 8192),
            'tensor_b_shape': (4096, 4096),
        },
        {
            'name': 'Case 3: Transposed',
            'tensor_a_shape': (2048, 1024),
            'tensor_b_shape': (1024, 1024),
        }
    ]
    
    for case in test_cases:
        print(f"\nTesting {case['name']}...")
        
        try:
            # Create test tensors
            a = torch.randn(case['tensor_a_shape'], device=device)
            b = torch.randn(case['tensor_b_shape'], device=device)
            
            print(f"  Tensor A shape: {a.shape}")
            print(f"  Tensor B shape: {b.shape}")
            
            # Try different matrix operations that might cause the error
            operations = [
                ('A.t() @ A', lambda: torch.mm(a.t(), a)),
                ('B.t() @ B', lambda: torch.mm(b.t(), b)),
                ('A @ B.t()', lambda: torch.mm(a, b.t())),
                ('A.t() @ B', lambda: torch.mm(a.t(), b)),
            ]
            
            for op_name, op_func in operations:
                try:
                    result = op_func()
                    print(f"    ✅ {op_name}: {result.shape}")
                except Exception as e:
                    print(f"    ❌ {op_name}: {e}")
                    
        except Exception as e:
            print(f"  ❌ Failed to create tensors: {e}")

if __name__ == "__main__":
    print("🔍 Debugging exact error scenario...")
    
    success1 = test_exact_error_scenario()
    test_matrix_multiplication_directly()
    
    if success1:
        print("\n✅ Exact error scenario test passed!")
    else:
        print("\n❌ Exact error scenario test failed!")
