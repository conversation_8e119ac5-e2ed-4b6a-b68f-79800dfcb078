#!/usr/bin/env python3
"""
Performance analysis tool for cluster-based influence function sampling.
This script helps users choose the optimal sampling interval for their specific use case.
"""

import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path


def analyze_cluster_distribution(dataset_path, cluster_column='cluster'):
    """Analyze cluster distribution in the dataset."""
    print(f"📊 Analyzing cluster distribution in {dataset_path}")
    
    try:
        df = pd.read_parquet(dataset_path)
        
        if cluster_column not in df.columns:
            print(f"❌ Cluster column '{cluster_column}' not found in dataset")
            print(f"Available columns: {list(df.columns)}")
            return None
        
        cluster_counts = df[cluster_column].value_counts()
        
        print(f"📈 Dataset Statistics:")
        print(f"  Total samples: {len(df)}")
        print(f"  Number of clusters: {len(cluster_counts)}")
        print(f"  Average samples per cluster: {cluster_counts.mean():.1f}")
        print(f"  Min samples per cluster: {cluster_counts.min()}")
        print(f"  Max samples per cluster: {cluster_counts.max()}")
        print(f"  Std deviation: {cluster_counts.std():.1f}")
        
        print(f"\n📋 Cluster Distribution:")
        for cluster, count in cluster_counts.head(10).items():
            print(f"  {cluster}: {count} samples")
        
        if len(cluster_counts) > 10:
            print(f"  ... and {len(cluster_counts) - 10} more clusters")
        
        return cluster_counts
        
    except Exception as e:
        print(f"❌ Error analyzing dataset: {e}")
        return None


def calculate_sampling_efficiency(cluster_counts, intervals):
    """Calculate sampling efficiency for different intervals."""
    results = []
    
    for interval in intervals:
        total_selected = 0
        total_samples = cluster_counts.sum()
        
        for cluster, count in cluster_counts.items():
            if interval == 1:
                selected = count
            else:
                selected = max(1, count // interval)
            total_selected += selected
        
        efficiency = (total_samples - total_selected) / total_samples * 100
        
        results.append({
            'interval': interval,
            'total_samples': total_samples,
            'selected_samples': total_selected,
            'efficiency_gain': efficiency,
            'computation_ratio': total_selected / total_samples
        })
    
    return pd.DataFrame(results)


def recommend_interval(cluster_counts, target_efficiency=80):
    """Recommend optimal sampling interval based on target efficiency."""
    intervals = range(1, 21)  # Test intervals 1-20
    
    best_interval = 1
    best_diff = float('inf')
    
    for interval in intervals:
        total_selected = 0
        total_samples = cluster_counts.sum()
        
        for count in cluster_counts:
            if interval == 1:
                selected = count
            else:
                selected = max(1, count // interval)
            total_selected += selected
        
        efficiency = (total_samples - total_selected) / total_samples * 100
        diff = abs(efficiency - target_efficiency)
        
        if diff < best_diff:
            best_diff = diff
            best_interval = interval
    
    return best_interval


def create_performance_visualization(efficiency_df, output_dir):
    """Create performance visualization charts."""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Set style
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # Create efficiency chart
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Efficiency gain chart
    ax1.plot(efficiency_df['interval'], efficiency_df['efficiency_gain'], 
             marker='o', linewidth=2, markersize=6)
    ax1.set_xlabel('Sampling Interval')
    ax1.set_ylabel('Efficiency Gain (%)')
    ax1.set_title('Efficiency Gain vs Sampling Interval')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(1, efficiency_df['interval'].max())
    
    # Add efficiency level lines
    for level, color in [(50, 'orange'), (80, 'green'), (90, 'red')]:
        ax1.axhline(y=level, color=color, linestyle='--', alpha=0.7, 
                   label=f'{level}% efficiency')
    ax1.legend()
    
    # Samples computed chart
    ax2.plot(efficiency_df['interval'], efficiency_df['selected_samples'], 
             marker='s', linewidth=2, markersize=6, color='coral')
    ax2.set_xlabel('Sampling Interval')
    ax2.set_ylabel('Samples Computed')
    ax2.set_title('Samples Computed vs Sampling Interval')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(1, efficiency_df['interval'].max())
    
    plt.tight_layout()
    plt.savefig(output_dir / 'sampling_efficiency_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create detailed table
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # Prepare table data
    table_data = efficiency_df.copy()
    table_data['efficiency_gain'] = table_data['efficiency_gain'].round(1)
    table_data['computation_ratio'] = table_data['computation_ratio'].round(3)
    
    table = ax.table(cellText=table_data.values,
                    colLabels=['Interval', 'Total Samples', 'Selected Samples', 
                              'Efficiency Gain (%)', 'Computation Ratio'],
                    cellLoc='center',
                    loc='center')
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    # Color code efficiency levels
    for i, row in table_data.iterrows():
        efficiency = row['efficiency_gain']
        if efficiency >= 90:
            color = '#ffcccc'  # Light red
        elif efficiency >= 80:
            color = '#ccffcc'  # Light green
        elif efficiency >= 50:
            color = '#ffffcc'  # Light yellow
        else:
            color = '#ffffff'  # White
        
        for j in range(len(table_data.columns)):
            table[(i+1, j)].set_facecolor(color)
    
    plt.title('Cluster Sampling Performance Analysis', fontsize=16, fontweight='bold', pad=20)
    plt.savefig(output_dir / 'sampling_performance_table.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Visualizations saved to {output_dir}/")


def main():
    parser = argparse.ArgumentParser(description='Analyze cluster-based influence sampling performance')
    parser.add_argument('dataset_path', help='Path to the dataset parquet file')
    parser.add_argument('--cluster-column', default='cluster', help='Name of the cluster column')
    parser.add_argument('--output-dir', default='./sampling_analysis', help='Output directory for results')
    parser.add_argument('--target-efficiency', type=float, default=80, 
                       help='Target efficiency percentage for recommendation')
    parser.add_argument('--max-interval', type=int, default=15, 
                       help='Maximum sampling interval to analyze')
    
    args = parser.parse_args()
    
    print("🔍 Cluster-Based Influence Sampling Performance Analyzer")
    print("=" * 60)
    
    # Analyze cluster distribution
    cluster_counts = analyze_cluster_distribution(args.dataset_path, args.cluster_column)
    if cluster_counts is None:
        return
    
    # Calculate efficiency for different intervals
    intervals = list(range(1, args.max_interval + 1))
    efficiency_df = calculate_sampling_efficiency(cluster_counts, intervals)
    
    print(f"\n📈 Sampling Efficiency Analysis:")
    print(efficiency_df.to_string(index=False))
    
    # Get recommendation
    recommended_interval = recommend_interval(cluster_counts, args.target_efficiency)
    recommended_efficiency = efficiency_df[efficiency_df['interval'] == recommended_interval]['efficiency_gain'].iloc[0]
    
    print(f"\n🎯 Recommendations:")
    print(f"  Target efficiency: {args.target_efficiency}%")
    print(f"  Recommended interval: {recommended_interval}")
    print(f"  Achieved efficiency: {recommended_efficiency:.1f}%")
    
    # Provide usage recommendations
    print(f"\n💡 Usage Recommendations:")
    
    if recommended_efficiency >= 90:
        print(f"  🚀 High efficiency setup - great for large models and long training")
    elif recommended_efficiency >= 80:
        print(f"  ⚖️  Balanced setup - recommended for most use cases")
    elif recommended_efficiency >= 50:
        print(f"  🎯 Conservative setup - good for critical applications")
    else:
        print(f"  📊 Low efficiency - consider using interval=1 (original behavior)")
    
    print(f"\n⚙️  Configuration:")
    print(f"  +data.influence_cluster_sampling_interval={recommended_interval}")
    print(f"  +data.influence_cluster_key={args.cluster_column}")
    
    # Create visualizations
    create_performance_visualization(efficiency_df, args.output_dir)
    
    # Save results
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    efficiency_df.to_csv(output_dir / 'sampling_efficiency_results.csv', index=False)
    
    print(f"\n📁 Results saved to: {args.output_dir}/")
    print(f"  - sampling_efficiency_results.csv")
    print(f"  - sampling_efficiency_analysis.png")
    print(f"  - sampling_performance_table.png")


if __name__ == "__main__":
    main()
