#!/usr/bin/env python3
"""
Simple test to verify that the Qwen3 fix works for the specific error case.
This replicates the exact error scenario from the original issue.
"""

import sys
import tempfile
import json
import os
import shutil


def test_original_error_scenario():
    """Test the exact scenario that was causing the original error"""
    print("Testing the original error scenario...")
    print("Original error: 'model type `qwen3` but Transformers does not recognize this architecture'")
    
    # Create a mock Qwen3 model directory (simulating a real Qwen3 model)
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create config.json with qwen3 model_type (this was causing the error)
        qwen3_config = {
            "model_type": "qwen3",  # This was the problematic line
            "architectures": ["Qwen3ForCausalLM"],
            "vocab_size": 151936,
            "hidden_size": 4096,
            "intermediate_size": 12288,
            "num_hidden_layers": 36,
            "num_attention_heads": 32,
            "num_key_value_heads": 8,
            "head_dim": 128,
            "attention_bias": False,
            "hidden_act": "silu",
            "max_position_embeddings": 40960,
            "rope_theta": 1000000,
            "rms_norm_eps": 1e-06,
            "use_cache": True,
            "tie_word_embeddings": False
        }
        
        config_path = os.path.join(temp_dir, "config.json")
        with open(config_path, 'w') as f:
            json.dump(qwen3_config, f, indent=2)
        
        print(f"✓ Created mock Qwen3 model at: {temp_dir}")
        print(f"  - Model type: {qwen3_config['model_type']}")
        print(f"  - Architecture: {qwen3_config['architectures'][0]}")
        
        # Import verl to initialize Qwen3 support
        print("\n🔧 Importing VERL to initialize Qwen3 support...")
        import verl
        
        # Now try the exact call that was failing
        print("\n📋 Testing AutoConfig.from_pretrained (this was failing before)...")
        from transformers import AutoConfig
        
        # This is the exact line from fsdp_workers.py:171 that was failing
        actor_model_config = AutoConfig.from_pretrained(temp_dir, trust_remote_code=False)
        
        print(f"✅ SUCCESS! AutoConfig.from_pretrained worked!")
        print(f"  - Loaded config type: {type(actor_model_config).__name__}")
        print(f"  - Model type: {actor_model_config.model_type}")
        print(f"  - Architectures: {getattr(actor_model_config, 'architectures', 'not set')}")
        
        # Verify Qwen3-specific fields are preserved
        if hasattr(actor_model_config, 'head_dim'):
            print(f"  - Head dim: {actor_model_config.head_dim}")
        if hasattr(actor_model_config, 'attention_bias'):
            print(f"  - Attention bias: {actor_model_config.attention_bias}")
        
        return True
        
    except ValueError as e:
        if "model type `qwen3`" in str(e) and "does not recognize this architecture" in str(e):
            print(f"❌ FAILED: Original error still occurs: {e}")
            return False
        else:
            print(f"❌ FAILED: Different error occurred: {e}")
            return False
    except Exception as e:
        print(f"❌ FAILED: Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        shutil.rmtree(temp_dir)


def test_qwen3_vs_qwen2_compatibility():
    """Test that Qwen3 models are treated as compatible with Qwen2"""
    print("\n" + "="*50)
    print("Testing Qwen3 vs Qwen2 compatibility...")
    
    try:
        import verl
        from transformers import AutoConfig
        
        # Create two temporary directories
        qwen2_dir = tempfile.mkdtemp()
        qwen3_dir = tempfile.mkdtemp()
        
        try:
            # Create Qwen2 config
            qwen2_config = {
                "model_type": "qwen2",
                "architectures": ["Qwen2ForCausalLM"],
                "vocab_size": 151936,
                "hidden_size": 4096,
                "num_hidden_layers": 36,
                "num_attention_heads": 32,
                "num_key_value_heads": 8,
            }
            
            # Create Qwen3 config
            qwen3_config = {
                "model_type": "qwen3",
                "architectures": ["Qwen3ForCausalLM"],
                "vocab_size": 151936,
                "hidden_size": 4096,
                "num_hidden_layers": 36,
                "num_attention_heads": 32,
                "num_key_value_heads": 8,
                "head_dim": 128,
                "attention_bias": False,
            }
            
            # Save configs
            with open(os.path.join(qwen2_dir, "config.json"), 'w') as f:
                json.dump(qwen2_config, f)
            with open(os.path.join(qwen3_dir, "config.json"), 'w') as f:
                json.dump(qwen3_config, f)
            
            # Load both configs
            config2 = AutoConfig.from_pretrained(qwen2_dir)
            config3 = AutoConfig.from_pretrained(qwen3_dir)
            
            print(f"✓ Qwen2 config loaded: {type(config2).__name__}")
            print(f"✓ Qwen3 config loaded: {type(config3).__name__}")
            
            # Both should have similar structure
            print(f"  - Qwen2 hidden_size: {config2.hidden_size}")
            print(f"  - Qwen3 hidden_size: {config3.hidden_size}")
            
            # Qwen3 should have additional fields
            if hasattr(config3, 'head_dim'):
                print(f"  - Qwen3 head_dim: {config3.head_dim}")
            if hasattr(config3, 'attention_bias'):
                print(f"  - Qwen3 attention_bias: {config3.attention_bias}")
            
            return True
            
        finally:
            shutil.rmtree(qwen2_dir)
            shutil.rmtree(qwen3_dir)
            
    except Exception as e:
        print(f"❌ Compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run the verification tests"""
    print("🧪 Qwen3 Fix Verification Test")
    print("="*50)
    print("This test verifies that the original Qwen3 error has been fixed.")
    print()
    
    # Test the original error scenario
    test1_result = test_original_error_scenario()
    
    # Test compatibility
    test2_result = test_qwen3_vs_qwen2_compatibility()
    
    print("\n" + "="*50)
    print("VERIFICATION RESULTS")
    print("="*50)
    
    if test1_result:
        print("✅ Original error scenario: FIXED")
    else:
        print("❌ Original error scenario: STILL FAILING")
    
    if test2_result:
        print("✅ Qwen3/Qwen2 compatibility: WORKING")
    else:
        print("❌ Qwen3/Qwen2 compatibility: FAILING")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED! The Qwen3 fix is working correctly.")
        print("   You can now use Qwen3 models in your training without the original error.")
        return 0
    else:
        print("\n❌ SOME TESTS FAILED! The fix may need more work.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
