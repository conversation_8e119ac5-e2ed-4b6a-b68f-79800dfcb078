#!/usr/bin/env python3
"""
Test with enhanced debugging to catch the exact dimension mismatch error.
"""

import torch
import torch.nn as nn
import logging

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_with_enhanced_debugging():
    """Test with enhanced debugging to catch dimension mismatch errors."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    # Create a model that might trigger the dimension mismatch
    # Based on the error logs, we need to create a scenario where:
    # 1. Input shape is [1, 1024, 1536]
    # 2. We have Linear(1536, 1536), Linear(1536, 256), Linear(1536, 256)
    # 3. Somewhere in the computation we get 2048 vs 1024 mismatch
    
    class ProblematicModel(nn.Module):
        def __init__(self):
            super().__init__()
            # These match the error log exactly
            self.layer1 = nn.Linear(1536, 1536)  # Linear(1536, 1536)
            self.layer2 = nn.Linear(1536, 256)   # Linear(1536, 256)
            self.layer3 = nn.Linear(1536, 256)   # Linear(1536, 256)
            
            # Add some additional layers that might cause dimension issues
            self.layer4 = nn.Linear(256, 512)    # This might create the 2048 dimension
            self.layer5 = nn.Linear(512, 1024)   # This might create the 1024 dimension
            
        def forward(self, input_ids, attention_mask=None, **kwargs):
            # Create the exact input shape: [1, 1024, 1536]
            x = torch.randn(1, 1024, 1536, device=input_ids.device)
            
            # Apply transformations that might cause dimension mismatches
            x1 = self.layer1(x)  # [1, 1024, 1536] -> [1, 1024, 1536]
            x2 = self.layer2(x)  # [1, 1024, 1536] -> [1, 1024, 256]
            x3 = self.layer3(x)  # [1, 1024, 1536] -> [1, 1024, 256]
            
            # Create a scenario that might lead to 2048 vs 1024 mismatch
            # Concatenate x2 and x3 to get 512 dimensions
            x_combined = torch.cat([x2, x3], dim=-1)  # [1, 1024, 512]
            
            # Apply more layers
            x4 = self.layer4(x_combined)  # [1, 1024, 512] -> [1, 1024, 512]
            x5 = self.layer5(x4)          # [1, 1024, 512] -> [1, 1024, 1024]
            
            # Create a final output that might cause issues
            # Concatenate x1 and x5 to get 2560 dimensions, then slice to 2048
            final_concat = torch.cat([x1, x5], dim=-1)  # [1, 1024, 2560]
            logits = final_concat[:, :, :2048]  # [1, 1024, 2048] - this creates the 2048!
            
            return type('Output', (), {'logits': logits})()
    
    model = ProblematicModel().to(device)
    
    print("Model architecture:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            print(f"  {name}: {module.in_features} -> {module.out_features}")
    
    # Create test data
    batch = {
        'input_ids': torch.randint(0, 1000, (1, 512)).to(device),
        'attention_mask': torch.ones(1, 512).to(device)
    }
    
    training_sample = {
        'input_ids': torch.randint(0, 1000, (1, 512)).to(device),
        'attention_mask': torch.ones(1, 512).to(device)
    }
    
    def loss_fn(model_output, batch_data):
        logits = model_output.logits  # [1, 1024, 2048]
        # Create labels that match
        labels = torch.randint(0, 2048, (logits.shape[0], logits.shape[1])).to(logits.device)
        return torch.nn.functional.cross_entropy(
            logits.view(-1, logits.size(-1)),
            labels.view(-1)
        )
    
    print("\n=== Testing with Enhanced Debugging ===")
    
    try:
        from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
        
        calculator = KFACInfluenceFunctionCalculator(
            model=model,
            regularization_lambda=1e-3,
            kfac_damping=1e-3,
            use_kfac=True
        )
        
        print("Testing K-FAC factor extraction...")
        calculator.extract_kfac_factors(batch, loss_fn)
        
        if calculator.kfac_factors:
            print(f"✅ K-FAC factor extraction successful: {len(calculator.kfac_factors)} factors")
            for key, factor in calculator.kfac_factors.items():
                print(f"  {key}: {factor.shape}")
        else:
            print("❌ No K-FAC factors extracted")
            return False
        
        print("\nTesting influence score computation...")
        calculator.compute_validation_gradients(batch, loss_fn)
        
        influence_score = calculator.compute_influence_score(
            training_sample, batch, loss_fn
        )
        
        print(f"✅ Influence score computed: {influence_score:.6f}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_dimension_combinations():
    """Test specific dimension combinations that might cause the error."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n=== Testing Specific Dimension Combinations ===")
    
    # Test cases that might reproduce the exact error
    test_cases = [
        {
            'name': 'Case 1: 2048 input, 1024 output',
            'model_layers': [(2048, 1024)],
            'input_shape': (1, 512, 2048),
        },
        {
            'name': 'Case 2: 8192 input, 4096 output', 
            'model_layers': [(8192, 4096)],
            'input_shape': (1, 256, 8192),
        },
        {
            'name': 'Case 3: Multiple layers with problematic dimensions',
            'model_layers': [(1536, 2048), (2048, 1024)],
            'input_shape': (1, 1024, 1536),
        }
    ]
    
    for case in test_cases:
        print(f"\nTesting {case['name']}...")
        
        class TestModel(nn.Module):
            def __init__(self, layers):
                super().__init__()
                self.layers = nn.ModuleList([
                    nn.Linear(in_dim, out_dim) for in_dim, out_dim in layers
                ])
                
            def forward(self, input_ids, **kwargs):
                x = torch.randn(case['input_shape'], device=input_ids.device)
                for layer in self.layers:
                    x = layer(x)
                return type('Output', (), {'logits': x})()
        
        model = TestModel(case['model_layers']).to(device)
        
        batch = {
            'input_ids': torch.randint(0, 100, (1, 32)).to(device),
            'attention_mask': torch.ones(1, 32).to(device)
        }
        
        def loss_fn(model_output, batch_data):
            logits = model_output.logits
            target_shape = logits.shape[:-1]
            labels = torch.randint(0, logits.shape[-1], target_shape).to(logits.device)
            return torch.nn.functional.cross_entropy(
                logits.view(-1, logits.size(-1)),
                labels.view(-1)
            )
        
        try:
            from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
            
            calculator = KFACInfluenceFunctionCalculator(
                model=model,
                regularization_lambda=1e-3,
                kfac_damping=1e-3,
                use_kfac=True
            )
            
            calculator.extract_kfac_factors(batch, loss_fn)
            
            if calculator.kfac_factors:
                print(f"  ✅ {case['name']}: K-FAC extraction successful")
                
                # Test influence score computation
                calculator.compute_validation_gradients(batch, loss_fn)
                training_sample = {
                    'input_ids': torch.randint(0, 100, (1, 32)).to(device),
                    'attention_mask': torch.ones(1, 32).to(device)
                }
                
                influence_score = calculator.compute_influence_score(
                    training_sample, batch, loss_fn
                )
                print(f"    Influence score: {influence_score:.6f}")
            else:
                print(f"  ⚠️ {case['name']}: No factors extracted")
                
        except Exception as e:
            print(f"  ❌ {case['name']}: Failed with error: {e}")
            if "size of tensor" in str(e) and "must match" in str(e):
                print(f"    🎯 This reproduced the dimension mismatch error!")
                import traceback
                traceback.print_exc()
                return False
    
    return True

if __name__ == "__main__":
    print("🔍 Testing with enhanced debugging...")
    
    success1 = test_with_enhanced_debugging()
    success2 = test_specific_dimension_combinations()
    
    if success1 and success2:
        print("\n✅ All enhanced debugging tests passed!")
    else:
        print("\n❌ Some enhanced debugging tests failed!")
