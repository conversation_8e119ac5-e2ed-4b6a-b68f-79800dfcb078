"""
Influence Function Computation Module

This module implements influence functions for curriculum learning, replacing perplexity-based
bandit updates with influence function calculations using Conjugate Gradient (CG) method.

The influence function measures how a training sample z affects the loss on validation set D_r:
I_θ(D_r, z) = -∇_θ L(θ, D_r) (H_θ + λI)^(-1) ∇_θ L(θ, z)

Where:
- I_θ(D_r, z) is the influence function
- L(θ, D_r) is the loss function evaluated on validation set D_r
- L(θ, z) is the loss function evaluated on training sample z
- θ represents the model parameters
- H_θ is the Hessian matrix of the loss with respect to parameters
- λ is a regularization parameter
- I is the identity matrix

The implementation uses Conjugate Gradient (CG) method to solve the linear system
(H_θ + λI)^(-1) ∇_θ L(θ, z) efficiently without explicitly computing the Hessian inverse.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union, Callable
import numpy as np
from collections import defaultdict
import logging
import gc

logger = logging.getLogger(__name__)


class InfluenceFunctionCalculator:
    """
    Base influence function calculator using Conjugate Gradient (CG) method.

    This calculator implements the influence function formula:
    I_θ(D_r, z) = -∇_θ L(θ, D_r) (H_θ + λI)^(-1) ∇_θ L(θ, z)

    The CG method is used to solve the linear system (H_θ + λI)^(-1) ∇_θ L(θ, z)
    without explicitly computing the Hessian inverse.
    """

    def __init__(self,
                 model: nn.Module,
                 regularization_lambda: float = 1e-3,
                 damping_factor: float = 1e-3,
                 cg_max_iterations: int = 10,
                 cg_tolerance: float = 1e-6,
                 max_samples_per_batch: int = 32,
                 freeze_attention_layers: bool = False,
                 **kwargs):
        """
        Initialize influence function calculator.

        Args:
            model: Neural network model
            regularization_lambda: Regularization parameter λ for (H_θ + λI)
            damping_factor: Additional damping for numerical stability
            cg_max_iterations: Maximum iterations for CG solver
            cg_tolerance: Convergence tolerance for CG solver
            max_samples_per_batch: Maximum samples to process in one batch
            freeze_attention_layers: Whether to freeze attention layers during influence computation
        """
        self.model = model
        self.regularization_lambda = regularization_lambda
        self.damping_factor = damping_factor
        self.cg_max_iterations = cg_max_iterations
        self.cg_tolerance = cg_tolerance
        self.max_samples_per_batch = max_samples_per_batch
        self.freeze_attention_layers = freeze_attention_layers

        # Cache for validation gradients
        self._validation_gradients = None
        self._validation_gradients_cached = False

        # Device management
        self.device = next(model.parameters()).device

        # Backward compatibility attributes for K-FAC interface
        self.use_kfac = kwargs.get('use_kfac', False)  # Ignored, but kept for compatibility
        self.kfac_damping = kwargs.get('kfac_damping', damping_factor)  # Ignored
        self.kfac_factors = {}  # Empty dict for compatibility

        # Store original attention layer states for freezing/unfreezing
        self._attention_layers_frozen = False
        self._original_attention_requires_grad = {}

    def _filter_model_inputs(self, batch: Dict) -> Dict:
        """
        Filter batch to only include inputs that the model expects.
        This prevents errors like 'unexpected keyword argument responses'.

        Also handles Ulysses sequence parallelism by properly slicing inputs
        when ulysses_sequence_parallel_size > 1.

        Note: We keep 'labels' in the filtered batch to maintain consistency
        between model inputs and loss computation.
        """
        from verl.utils.ulysses import get_ulysses_sequence_parallel_world_size, ulysses_pad_and_slice_inputs

        # Common model input keys for transformer models
        expected_keys = {
            'input_ids', 'attention_mask', 'position_ids', 'token_type_ids',
            'labels', 'past_key_values', 'use_cache', 'output_attentions',
            'output_hidden_states', 'return_dict'
        }

        # Filter batch to only include expected keys
        filtered_batch = {}
        for key, value in batch.items():
            if key in expected_keys:
                filtered_batch[key] = value
            else:
                # print(f"Filtering out unexpected model input: {key}")
                pass

        # Handle Ulysses sequence parallelism
        ulysses_sp_size = get_ulysses_sequence_parallel_world_size()
        if ulysses_sp_size > 1:
            # Process input_ids and position_ids for Ulysses sequence parallelism
            if 'input_ids' in filtered_batch:
                input_ids = filtered_batch['input_ids']
                position_ids = filtered_batch.get('position_ids', None)

                # Generate position_ids if not provided
                if position_ids is None:
                    seq_len = input_ids.size(1)
                    position_ids = torch.arange(seq_len, device=input_ids.device).unsqueeze(0).expand(input_ids.size(0), -1)

                # Pad and slice inputs for Ulysses sequence parallelism
                sliced_input_ids, sliced_position_ids, pad_size = ulysses_pad_and_slice_inputs(
                    input_ids, position_ids, ulysses_sp_size
                )

                filtered_batch['input_ids'] = sliced_input_ids
                if sliced_position_ids is not None:
                    filtered_batch['position_ids'] = sliced_position_ids

                # Also handle attention_mask if present
                if 'attention_mask' in filtered_batch:
                    attention_mask = filtered_batch['attention_mask']
                    if pad_size > 0:
                        # Pad attention mask with zeros (masked positions)
                        attention_mask = torch.nn.functional.pad(attention_mask, (0, pad_size), value=0)
                    # Slice attention mask
                    from verl.utils.ulysses import slice_input_tensor
                    sliced_attention_mask = slice_input_tensor(attention_mask, dim=1, padding=False)
                    filtered_batch['attention_mask'] = sliced_attention_mask

                # Handle labels if present
                if 'labels' in filtered_batch:
                    labels = filtered_batch['labels']
                    if pad_size > 0:
                        # Pad labels with -100 (ignore index)
                        labels = torch.nn.functional.pad(labels, (0, pad_size), value=-100)
                    # Slice labels
                    sliced_labels = slice_input_tensor(labels, dim=1, padding=False)
                    filtered_batch['labels'] = sliced_labels

        return filtered_batch

    def _freeze_attention_layers(self):
        """
        Freeze attention layers to reduce computational cost during influence computation.
        """
        if self._attention_layers_frozen:
            return

        print("Freezing attention layers for influence computation")
        self._original_attention_requires_grad = {}

        for name, param in self.model.named_parameters():
            # Identify attention-related parameters
            if any(attn_keyword in name.lower() for attn_keyword in
                   ['attn', 'attention', 'self_attn', 'cross_attn', 'q_proj', 'k_proj', 'v_proj', 'o_proj']):
                self._original_attention_requires_grad[name] = param.requires_grad
                param.requires_grad = False

        self._attention_layers_frozen = True

    def _unfreeze_attention_layers(self):
        """
        Restore original requires_grad state for attention layers.
        """
        if not self._attention_layers_frozen:
            return

        print("Unfreezing attention layers after influence computation")

        for name, param in self.model.named_parameters():
            if name in self._original_attention_requires_grad:
                param.requires_grad = self._original_attention_requires_grad[name]

        self._original_attention_requires_grad = {}
        self._attention_layers_frozen = False

    def compute_gradients(self, batch: Dict, loss_fn: Callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients for a given batch and loss function.

        Args:
            batch: Input batch dictionary
            loss_fn: Loss function to compute gradients for

        Returns:
            Dictionary of parameter gradients
        """
        # Freeze attention layers if requested
        if self.freeze_attention_layers:
            self._freeze_attention_layers()

        try:
            # Clear existing gradients
            self.model.zero_grad(set_to_none=True)

            # Filter batch to only include expected model inputs
            filtered_batch = self._filter_model_inputs(batch)

            # Forward pass and compute loss
            outputs = self.model(**filtered_batch)
            # Use filtered batch for loss computation to ensure consistency
            loss = loss_fn(outputs, filtered_batch)

            # Backward pass to compute gradients
            loss.backward()

            # Extract gradients
            gradients = {}
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    gradients[name] = param.grad.clone().detach()

            return gradients

        finally:
            # Always unfreeze attention layers after computation
            if self.freeze_attention_layers:
                self._unfreeze_attention_layers()

    def hessian_vector_product(self,
                             vector: Dict[str, torch.Tensor],
                             batch: Dict,
                             loss_fn: Callable,
                             damping: float = None) -> Dict[str, torch.Tensor]:
        """
        Compute Hessian-vector product (H_θ + λI) * v using automatic differentiation.

        Note: Attention layers are NOT frozen during Hessian computation as it requires
        gradients from all parameters for proper second-order derivatives.

        Args:
            vector: Vector to multiply with Hessian
            batch: Input batch for computing Hessian
            loss_fn: Loss function
            damping: Damping factor (uses self.regularization_lambda if None)

        Returns:
            Hessian-vector product
        """
        if damping is None:
            damping = self.regularization_lambda

        # Clear gradients
        self.model.zero_grad(set_to_none=True)

        # Filter batch to only include expected model inputs
        filtered_batch = self._filter_model_inputs(batch)

        # Forward pass
        outputs = self.model(**filtered_batch)
        # Use filtered batch for loss computation to ensure consistency
        loss = loss_fn(outputs, filtered_batch)

        # First-order gradients
        first_grads = torch.autograd.grad(loss, self.model.parameters(), create_graph=True, allow_unused=True)

        # Compute gradient-vector product
        grad_vector_product = 0.0
        for grad, (name, _) in zip(first_grads, self.model.named_parameters()):
            if grad is not None and name in vector:
                grad_vector_product += torch.sum(grad * vector[name])

        # Second-order gradients (Hessian-vector product)
        try:
            hvp_grads = torch.autograd.grad(grad_vector_product, self.model.parameters(), retain_graph=False)

            # Add damping term: (H + λI) * v = H * v + λ * v
            hvp = {}
            for (name, _), hvp_grad in zip(self.model.named_parameters(), hvp_grads):
                if name in vector:
                    hvp[name] = hvp_grad + damping * vector[name]

            return hvp

        except RuntimeError as e:
            if 'derivative' in str(e) and 'not implemented' in str(e):
                logger.warning(f'Second-order derivatives not available: {e}. Using identity approximation.')
                # Fallback: use identity matrix approximation (H ≈ λI)
                hvp = {}
                for name in vector.keys():
                    hvp[name] = damping * vector[name]
                return hvp
            else:
                raise

    def conjugate_gradient_solve(self,
                               b: Dict[str, torch.Tensor],
                               batch: Dict,
                               loss_fn: Callable) -> Dict[str, torch.Tensor]:
        """
        Solve (H_θ + λI) * x = b using Conjugate Gradient method.

        Args:
            b: Right-hand side vector
            batch: Input batch for Hessian computation
            loss_fn: Loss function

        Returns:
            Solution x such that (H_θ + λI) * x = b
        """
        # Initialize solution x = 0
        x = {name: torch.zeros_like(grad) for name, grad in b.items()}

        # Initial residual r = b - A*x = b (since x = 0)
        r = {name: grad.clone() for name, grad in b.items()}

        # Initial search direction p = r
        p = {name: grad.clone() for name, grad in r.items()}

        # Initial residual norm squared
        rsold = sum(torch.sum(r[name] * r[name]) for name in r.keys())

        for iteration in range(self.cg_max_iterations):
            # Compute A*p (Hessian-vector product)
            try:
                Ap = self.hessian_vector_product(p, batch, loss_fn)
            except RuntimeError as e:
                logger.warning(f"CG iteration {iteration}: Hessian computation failed: {e}")
                break

            # Compute step size alpha = rsold / (p^T * A * p)
            pAp = sum(torch.sum(p[name] * Ap[name]) for name in p.keys())

            if pAp <= 0:
                logger.warning(f"CG iteration {iteration}: Non-positive curvature detected")
                break

            alpha = rsold / pAp

            # Update solution: x = x + alpha * p
            for name in x.keys():
                x[name] = x[name] + alpha * p[name]

            # Update residual: r = r - alpha * A * p
            for name in r.keys():
                r[name] = r[name] - alpha * Ap[name]

            # Compute new residual norm squared
            rsnew = sum(torch.sum(r[name] * r[name]) for name in r.keys())

            # Check convergence
            if torch.sqrt(rsnew) < self.cg_tolerance:
                # print(f"CG converged in {iteration + 1} iterations")
                break

            # Compute beta for next iteration
            beta = rsnew / rsold

            # Update search direction: p = r + beta * p
            for name in p.keys():
                p[name] = r[name] + beta * p[name]

            rsold = rsnew

        return x

    def compute_validation_gradients(self, validation_batch: Dict, loss_fn: Callable):
        """
        Compute and cache validation gradients.

        Args:
            validation_batch: Validation data batch
            loss_fn: Loss function
        """
        try:
            self._validation_gradients = self.compute_gradients(validation_batch, loss_fn)
            self._validation_gradients_cached = True
            print("Validation gradients computed and cached")
        except Exception as e:
            logger.error(f"Failed to compute validation gradients: {e}")
            raise

    def compute_influence_score(self,
                              training_sample: Dict,
                              validation_batch: Dict,
                              loss_fn: Callable) -> float:
        """
        Compute influence score for a training sample with FSDP-compatible memory management.

        Args:
            training_sample: Single training sample
            validation_batch: Validation batch (used if validation gradients not cached)
            loss_fn: Loss function

        Returns:
            Influence score as a scalar
        """
        # Ensure validation gradients are available
        if not self._validation_gradients_cached:
            self.compute_validation_gradients(validation_batch, loss_fn)

        # Cache training sample for finite difference HVP
        self._cached_training_sample = training_sample

        try:
            # Memory management: Clear gradients before computation (WITHOUT torch.no_grad())
            self.model.zero_grad(set_to_none=True)

            # Compute training sample gradients with memory management
            training_gradients = self._compute_gradients_with_memory_management(training_sample, loss_fn)

            # 使用有限差分方法计算 (H_θ + λI)^(-1) * ∇_θ L(θ, z)
            # 首先尝试鲁棒的有限差分方法
            inverse_hvp = self._robust_finite_difference_hvp(
                training_gradients, None, self.regularization_lambda
            )

            # 如果有限差分失败，回退到CG方法
            if inverse_hvp is None:
                logger.warning("Finite difference HVP failed, falling back to CG method")
                inverse_hvp = self._conjugate_gradient_solve_with_memory_management(
                    training_gradients, validation_batch, loss_fn
                )

            # Compute influence score: -∇_θ L(θ, D_r)^T * (H_θ + λI)^(-1) * ∇_θ L(θ, z)
            influence_score = 0.0
            for name in self._validation_gradients.keys():
                if name in inverse_hvp:
                    val_grad = self._validation_gradients[name]
                    inv_hvp = inverse_hvp[name]

                    # Handle potential size mismatch due to Ulysses sequence parallelism
                    if val_grad.shape != inv_hvp.shape:
                        # Use the smaller tensor size to avoid out-of-bounds errors
                        min_numel = min(val_grad.numel(), inv_hvp.numel())
                        val_grad_flat = val_grad.flatten()[:min_numel]
                        inv_hvp_flat = inv_hvp.flatten()[:min_numel]
                        influence_score -= torch.sum(val_grad_flat * inv_hvp_flat).item()
                        continue

                    influence_score -= torch.sum(val_grad * inv_hvp).item()

            print(f"Computed influence score: {influence_score:.6f} Success!🌴🌴🌴🌴") # 注意
            return influence_score

        except torch.cuda.OutOfMemoryError as e:
            logger.warning(f"CUDA OOM in influence computation, trying fallback: {e}")
            # Clear everything and try with reduced precision
            self._clear_memory()
            return self._compute_influence_score_fallback(training_sample, validation_batch, loss_fn)

        except Exception as e:
            logger.warning(f"Failed to compute influence score: {e}")
            import traceback
            traceback.print_exc()
            return 0.0
        finally:
            # FSDP-compatible cleanup
            self._clear_memory()

    def _compute_gradients_with_memory_management(self, batch: Dict, loss_fn: Callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients with FSDP-compatible memory management.
        """
        # Freeze attention layers if requested
        if self.freeze_attention_layers:
            self._freeze_attention_layers()

        try:
            # Clear existing gradients (WITHOUT torch.no_grad())
            self.model.zero_grad(set_to_none=True)

            # Filter batch to only include expected model inputs
            filtered_batch = self._filter_model_inputs(batch)

            # Forward pass and compute loss
            outputs = self.model(**filtered_batch)
            # Use filtered batch for loss computation to ensure consistency
            loss = loss_fn(outputs, filtered_batch)

            # Backward pass to compute gradients
            loss.backward()

            # Extract gradients with memory management and ensure device/dtype consistency
            gradients = {}
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    # Ensure gradient has same device and dtype as parameter
                    gradients[name] = param.grad.clone().detach().to(device=param.device, dtype=param.dtype)
                    # Clear gradient immediately after cloning to save memory
                    param.grad = None

            return gradients

        except torch.cuda.OutOfMemoryError:
            # Clear memory and re-raise
            self._clear_memory()
            raise
        finally:
            # Always unfreeze attention layers after computation
            if self.freeze_attention_layers:
                self._unfreeze_attention_layers()

    def _conjugate_gradient_solve_with_memory_management(self,
                                                       b: Dict[str, torch.Tensor],
                                                       batch: Dict,
                                                       loss_fn: Callable) -> Dict[str, torch.Tensor]:
        """
        CG solver with enhanced memory management for FSDP compatibility.
        """
        # Initialize solution x = 0
        x = {name: torch.zeros_like(grad) for name, grad in b.items()}

        # Initial residual r = b - A*x = b (since x = 0)
        r = {name: grad.clone() for name, grad in b.items()}

        # Initial search direction p = r
        p = {name: grad.clone() for name, grad in r.items()}

        # Initial residual norm squared
        rsold = sum(torch.sum(r[name] * r[name]) for name in r.keys())

        for iteration in range(self.cg_max_iterations):
            try:
                # Clear gradients before Hessian computation (WITHOUT torch.no_grad())
                self.model.zero_grad(set_to_none=True)

                # Compute A*p (Hessian-vector product) with memory management
                Ap = self._hessian_vector_product_with_memory_management(p, batch, loss_fn)

                # Compute step size alpha = rsold / (p^T * A * p)
                pAp = sum(torch.sum(p[name] * Ap[name]) for name in p.keys())

                if pAp <= 0:
                    logger.warning(f"CG iteration {iteration}: Non-positive curvature detected")
                    break

                alpha = rsold / pAp

                # Update solution: x = x + alpha * p
                for name in x.keys():
                    x[name] = x[name] + alpha * p[name]

                # Update residual: r = r - alpha * A * p
                for name in r.keys():
                    r[name] = r[name] - alpha * Ap[name]

                # Clear Ap to save memory
                del Ap

                # Compute new residual norm squared
                rsnew = sum(torch.sum(r[name] * r[name]) for name in r.keys())

                # Check convergence
                if torch.sqrt(rsnew) < self.cg_tolerance:
                    # print(f"CG converged in {iteration + 1} iterations")
                    break

                # Compute beta for next iteration
                beta = rsnew / rsold

                # Update search direction: p = r + beta * p
                for name in p.keys():
                    p[name] = r[name] + beta * p[name]

                rsold = rsnew

                # Periodic memory cleanup
                if iteration % 10 == 0:
                    torch.cuda.empty_cache()

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"CG iteration {iteration}: OOM, breaking early")
                break

        return x

    def _hessian_vector_product_with_memory_management(self,
                                                     vector: Dict[str, torch.Tensor],
                                                     batch: Dict,
                                                     loss_fn: Callable,
                                                     damping: float = None) -> Dict[str, torch.Tensor]:
        """
        Compute Hessian-vector product with enhanced memory management.
    
        Handles cases where second-order derivatives are not available (e.g., efficient attention).
        """
        if damping is None:
            damping = self.regularization_lambda
    
        try:
            # Clear gradients
            with torch.no_grad():
                self.model.zero_grad(set_to_none=True)
    
            # Filter batch to only include expected model inputs
            filtered_batch = self._filter_model_inputs(batch)
    
            # Forward pass
            outputs = self.model(**filtered_batch)
            # Use filtered batch for loss computation to ensure consistency
            loss = loss_fn(outputs, filtered_batch)
            
            # Check if model is wrapped with FSDP
            is_fsdp = any('_fsdp_wrapped_module' in name for name, _ in self.model.named_parameters())
            
            if is_fsdp:
                logger.info("Detected FSDP model, using FSDP-compatible gradient computation")
                return self._hessian_vector_product_fsdp_compatible(vector, loss, damping)
            else:
                return self._hessian_vector_product_standard(vector, loss, damping)
    
        except torch.cuda.OutOfMemoryError:
            # Clear memory and re-raise
            self._clear_memory()
            raise

    def _hessian_vector_product_fsdp_compatible(self,
                                          vector: Dict[str, torch.Tensor],
                                          loss: torch.Tensor,
                                          damping: float) -> Dict[str, torch.Tensor]:
        """
        FSDP-compatible Hessian-vector product computation.
        使用真正的有限差分方法计算Hessian-vector乘积，适用于多GPU Ulysses并行计算。
        """
        try:
            # 保存原始梯度状态
            original_grads = self._save_gradients()

            # 尝试使用改进的有限差分方法
            hvp = self._robust_finite_difference_hvp(vector, loss, damping)
            if hvp is not None:
                self._restore_gradients(original_grads)
                return hvp

            # 如果有限差分失败，使用对角近似
            logger.warning("Finite difference failed, using diagonal approximation")
            hvp = self._diagonal_approximation_hvp(vector, loss, damping)
            self._restore_gradients(original_grads)
            return hvp

        except Exception as e:
            logger.error(f'All FSDP HVP methods failed: {e}. Using identity approximation.')
            hvp = {}
            for name in vector.keys():
                hvp[name] = damping * vector[name]
            try:
                self._restore_gradients(original_grads)
            except:
                pass
            return hvp

    def _save_gradients(self) -> Dict[str, torch.Tensor]:
        """保存当前梯度状态"""
        saved_grads = {}
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                # Ensure saved gradient maintains device and dtype consistency
                saved_grads[name] = param.grad.clone().detach().to(device=param.device, dtype=param.dtype)
        return saved_grads

    def _restore_gradients(self, saved_grads: Dict[str, torch.Tensor]):
        """恢复梯度状态，确保设备和数据类型一致性"""
        try:
            for name, param in self.model.named_parameters():
                if name in saved_grads:
                    # Ensure restored gradient has same device and dtype as parameter
                    restored_grad = saved_grads[name].clone().to(device=param.device, dtype=param.dtype)
                    param.grad = restored_grad
                else:
                    param.grad = None
        except Exception as e:
            logger.warning(f"Failed to restore gradients: {e}")
            # Clear all gradients to ensure consistency
            self.model.zero_grad(set_to_none=True)

    def _improved_finite_difference_hvp(self, vector: Dict[str, torch.Tensor],
                                      loss: torch.Tensor, damping: float) -> Optional[Dict[str, torch.Tensor]]:
        """
        改进的有限差分方法，适用于多GPU Ulysses并行计算。
        使用同步机制确保在分布式环境下的正确性。
        """
        try:
            from verl.utils.ulysses import get_ulysses_sequence_parallel_group, get_ulysses_sequence_parallel_world_size
            import torch.distributed as dist

            eps = 1e-4  # 有限差分步长

            # 检查是否有缓存的训练样本
            if not hasattr(self, '_cached_training_sample') or self._cached_training_sample is None:
                logger.debug("No cached training sample for finite difference HVP")
                return None

            # 获取Ulysses并行组信息
            ulysses_group = get_ulysses_sequence_parallel_group()
            ulysses_world_size = get_ulysses_sequence_parallel_world_size()

            # 保存当前参数状态
            original_params = {}
            for name, param in self.model.named_parameters():
                if name in vector and param.requires_grad:
                    original_params[name] = param.data.clone().detach()

            # 确保所有GPU上的参数同步
            if ulysses_group is not None and ulysses_world_size > 1:
                for name, param in self.model.named_parameters():
                    if name in vector and param.requires_grad:
                        dist.all_reduce(param.data, group=ulysses_group)
                        param.data /= ulysses_world_size

            # 计算 f(x + eps * v) 的梯度
            self._apply_parameter_perturbation(vector, eps, original_params)

            # 同步参数修改
            if ulysses_group is not None and ulysses_world_size > 1:
                self._synchronize_parameters(vector, ulysses_group)

            grad_plus = self._compute_gradients_with_distributed_sync(self._cached_training_sample)

            # 计算 f(x - eps * v) 的梯度
            self._apply_parameter_perturbation(vector, -eps, original_params)

            # 同步参数修改
            if ulysses_group is not None and ulysses_world_size > 1:
                self._synchronize_parameters(vector, ulysses_group)

            grad_minus = self._compute_gradients_with_distributed_sync(self._cached_training_sample)

            # 恢复原始参数
            self._restore_parameters(original_params)

            # 同步参数恢复
            if ulysses_group is not None and ulysses_world_size > 1:
                self._synchronize_parameters(vector, ulysses_group)

            # 计算有限差分: (grad_plus - grad_minus) / (2 * eps)
            hvp = self._compute_finite_difference_result(grad_plus, grad_minus, vector, eps, damping)

            return hvp

        except Exception as e:
            logger.debug(f"Improved finite difference HVP failed: {e}")
            # 确保参数状态恢复
            try:
                if 'original_params' in locals():
                    self._restore_parameters(original_params)
            except:
                pass
            return None

    def _finite_difference_hvp(self, vector: Dict[str, torch.Tensor],
                            loss: torch.Tensor, damping: float) -> Optional[Dict[str, torch.Tensor]]:
        """使用有限差分方法计算Hessian-vector乘积（保留原方法作为备用）"""
        try:
            eps = 1e-4  # 有限差分步长

            # 检查是否有缓存的训练样本用于重新计算损失
            if not hasattr(self, '_cached_training_sample') or self._cached_training_sample is None:
                logger.debug("No cached training sample for finite difference HVP")
                return None

            # 保存当前参数
            original_params = {}
            for name, param in self.model.named_parameters():
                if name in vector:
                    original_params[name] = param.data.clone()

            # 计算 f(x + eps * v) 的梯度
            for name, param in self.model.named_parameters():
                if name in vector:
                    param.data.add_(vector[name], alpha=eps)

            # 重新计算损失和梯度
            self.model.zero_grad(set_to_none=True)
            grad_plus = self._compute_gradients_with_forward_pass(self._cached_training_sample)

            # 恢复参数并计算 f(x - eps * v) 的梯度
            for name, param in self.model.named_parameters():
                if name in vector:
                    param.data.copy_(original_params[name])
                    param.data.add_(vector[name], alpha=-eps)

            self.model.zero_grad(set_to_none=True)
            grad_minus = self._compute_gradients_with_forward_pass(self._cached_training_sample)

            # 恢复原始参数
            for name, param in self.model.named_parameters():
                if name in vector:
                    param.data.copy_(original_params[name])

            # 计算有限差分: (grad_plus - grad_minus) / (2 * eps)
            hvp = {}
            for name in vector.keys():
                if name in grad_plus and name in grad_minus:
                    grad_plus_tensor = grad_plus[name]
                    grad_minus_tensor = grad_minus[name]

                    # Handle potential size mismatch due to Ulysses sequence parallelism
                    if grad_plus_tensor.shape != grad_minus_tensor.shape:
                        min_numel = min(grad_plus_tensor.numel(), grad_minus_tensor.numel())
                        grad_plus_flat = grad_plus_tensor.flatten()[:min_numel]
                        grad_minus_flat = grad_minus_tensor.flatten()[:min_numel]
                        diff_flat = (grad_plus_flat - grad_minus_flat) / (2 * eps)

                        # Reshape back to match vector shape
                        if name in vector:
                            vec = vector[name]
                            if vec.numel() <= min_numel:
                                hvp[name] = diff_flat[:vec.numel()].view_as(vec) + damping * vec
                            else:
                                result_tensor = damping * vec
                                result_tensor.flatten()[:min_numel] = diff_flat
                                hvp[name] = result_tensor
                        else:
                            hvp[name] = diff_flat.view_as(grad_plus_tensor) + damping * vector[name]
                    else:
                        hvp[name] = (grad_plus_tensor - grad_minus_tensor) / (2 * eps) + damping * vector[name]
                else:
                    hvp[name] = damping * vector[name]

            return hvp

        except Exception as e:
            logger.debug(f"Finite difference HVP failed: {e}")
            return None

    def _improved_finite_difference_hvp_with_timeout(self, vector: Dict[str, torch.Tensor],
                                                   loss: torch.Tensor, damping: float,
                                                   timeout: float = 30.0) -> Optional[Dict[str, torch.Tensor]]:
        """
        带超时保护的改进有限差分方法，防止在多GPU环境下死锁。
        """
        import threading

        result = [None]  # 使用列表来存储结果，以便在线程间共享
        exception = [None]

        def compute_hvp():
            try:
                result[0] = self._improved_finite_difference_hvp(vector, loss, damping)
            except Exception as e:
                exception[0] = e

        # 使用线程执行计算，避免信号处理的复杂性
        compute_thread = threading.Thread(target=compute_hvp)
        compute_thread.daemon = True
        compute_thread.start()

        # 等待计算完成或超时
        compute_thread.join(timeout=timeout)

        if compute_thread.is_alive():
            logger.warning(f"Finite difference HVP computation timed out after {timeout}s")
            # 线程仍在运行，说明超时了
            return None

        if exception[0] is not None:
            logger.debug(f"Finite difference HVP failed with exception: {exception[0]}")
            return None

        return result[0]

    def _robust_finite_difference_hvp(self, vector: Dict[str, torch.Tensor],
                                    loss: torch.Tensor, damping: float) -> Optional[Dict[str, torch.Tensor]]:
        """
        鲁棒的有限差分方法，专门为多GPU Ulysses并行环境设计。
        使用分段计算和错误恢复机制确保稳定性。
        """
        try:
            from verl.utils.ulysses import get_ulysses_sequence_parallel_group, get_ulysses_sequence_parallel_world_size
            import torch.distributed as dist

            eps = 1e-4  # 有限差分步长

            # 检查是否有缓存的训练样本
            if not hasattr(self, '_cached_training_sample') or self._cached_training_sample is None:
                logger.debug("No cached training sample for finite difference HVP")
                return None

            # 获取Ulysses并行组信息
            ulysses_group = get_ulysses_sequence_parallel_group()
            ulysses_world_size = get_ulysses_sequence_parallel_world_size()

            # 保存当前参数状态
            original_params = self._save_model_parameters(vector)

            try:
                # 分段计算有限差分，避免一次性修改所有参数
                hvp_result = {}

                # 将参数分组处理，减少内存压力
                param_groups = self._group_parameters_by_size(vector)

                for group_name, group_params in param_groups.items():
                    logger.debug(f"Processing parameter group: {group_name}")

                    # 计算这个组的HVP
                    group_hvp = self._compute_group_finite_difference(
                        group_params, eps, original_params, ulysses_group, ulysses_world_size
                    )

                    if group_hvp is None:
                        logger.warning(f"Failed to compute HVP for group {group_name}")
                        return None

                    hvp_result.update(group_hvp)

                # 添加阻尼项
                for name in vector.keys():
                    if name in hvp_result:
                        hvp_result[name] += damping * vector[name]
                    else:
                        hvp_result[name] = damping * vector[name]

                return hvp_result

            finally:
                # 确保参数恢复
                self._restore_model_parameters(original_params)

        except Exception as e:
            logger.debug(f"Robust finite difference HVP failed: {e}")
            return None

    def _save_model_parameters(self, vector: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """保存模型参数状态"""
        original_params = {}
        for name, param in self.model.named_parameters():
            if name in vector and param.requires_grad:
                original_params[name] = param.data.clone().detach()
        return original_params

    def _restore_model_parameters(self, original_params: Dict[str, torch.Tensor]):
        """恢复模型参数状态"""
        for name, param in self.model.named_parameters():
            if name in original_params and param.requires_grad:
                param.data.copy_(original_params[name])

    def _group_parameters_by_size(self, vector: Dict[str, torch.Tensor]) -> Dict[str, Dict[str, torch.Tensor]]:
        """将参数按大小分组，避免一次性处理过多参数"""
        groups = {}
        current_group = {}
        current_size = 0
        group_idx = 0
        max_group_size = 50 * 1024 * 1024  # 50MB per group

        for name, tensor in vector.items():
            tensor_size = tensor.numel() * tensor.element_size()

            if current_size + tensor_size > max_group_size and current_group:
                groups[f"group_{group_idx}"] = current_group
                current_group = {}
                current_size = 0
                group_idx += 1

            current_group[name] = tensor
            current_size += tensor_size

        if current_group:
            groups[f"group_{group_idx}"] = current_group

        return groups

    def _compute_group_finite_difference(self, group_params: Dict[str, torch.Tensor],
                                       eps: float, original_params: Dict[str, torch.Tensor],
                                       ulysses_group, ulysses_world_size: int) -> Optional[Dict[str, torch.Tensor]]:
        """计算参数组的有限差分"""
        try:
            import torch.distributed as dist

            # 计算 f(x + eps * v) 的梯度
            self._apply_group_perturbation(group_params, eps, original_params)

            # 同步参数修改
            if ulysses_group is not None and ulysses_world_size > 1:
                self._sync_group_parameters(group_params, ulysses_group)

            grad_plus = self._compute_gradients_safe(self._cached_training_sample)
            if grad_plus is None:
                return None

            # 计算 f(x - eps * v) 的梯度
            self._apply_group_perturbation(group_params, -eps, original_params)

            # 同步参数修改
            if ulysses_group is not None and ulysses_world_size > 1:
                self._sync_group_parameters(group_params, ulysses_group)

            grad_minus = self._compute_gradients_safe(self._cached_training_sample)
            if grad_minus is None:
                return None

            # 恢复原始参数
            self._restore_group_parameters(group_params, original_params)

            # 计算有限差分: (grad_plus - grad_minus) / (2 * eps)
            hvp = {}
            for name in group_params.keys():
                if name in grad_plus and name in grad_minus:
                    grad_plus_tensor = grad_plus[name]
                    grad_minus_tensor = grad_minus[name]

                    # 处理Ulysses并行可能导致的大小不匹配
                    if grad_plus_tensor.shape != grad_minus_tensor.shape:
                        min_numel = min(grad_plus_tensor.numel(), grad_minus_tensor.numel())
                        grad_plus_flat = grad_plus_tensor.flatten()[:min_numel]
                        grad_minus_flat = grad_minus_tensor.flatten()[:min_numel]
                        diff_flat = (grad_plus_flat - grad_minus_flat) / (2 * eps)

                        # 重新整形以匹配向量形状
                        vec = group_params[name]
                        if vec.numel() <= min_numel:
                            hvp[name] = diff_flat[:vec.numel()].view_as(vec)
                        else:
                            result_tensor = torch.zeros_like(vec)
                            result_tensor.flatten()[:min_numel] = diff_flat
                            hvp[name] = result_tensor
                    else:
                        hvp[name] = (grad_plus_tensor - grad_minus_tensor) / (2 * eps)
                else:
                    hvp[name] = torch.zeros_like(group_params[name])

            return hvp

        except Exception as e:
            logger.debug(f"Group finite difference failed: {e}")
            return None

    def _apply_group_perturbation(self, group_params: Dict[str, torch.Tensor],
                                eps: float, original_params: Dict[str, torch.Tensor]):
        """对参数组应用扰动"""
        for name, param in self.model.named_parameters():
            if name in group_params and name in original_params and param.requires_grad:
                param.data.copy_(original_params[name])
                param.data.add_(group_params[name], alpha=eps)

    def _sync_group_parameters(self, group_params: Dict[str, torch.Tensor], ulysses_group):
        """同步参数组"""
        import torch.distributed as dist
        try:
            handles = []
            for name, param in self.model.named_parameters():
                if name in group_params and param.requires_grad:
                    handle = dist.all_reduce(param.data, group=ulysses_group, async_op=True)
                    handles.append(handle)

            for handle in handles:
                handle.wait()
        except Exception as e:
            logger.warning(f"Parameter group synchronization failed: {e}")

    def _restore_group_parameters(self, group_params: Dict[str, torch.Tensor],
                                original_params: Dict[str, torch.Tensor]):
        """恢复参数组"""
        for name, param in self.model.named_parameters():
            if name in group_params and name in original_params and param.requires_grad:
                param.data.copy_(original_params[name])

    def _compute_gradients_safe(self, training_sample: Dict) -> Optional[Dict[str, torch.Tensor]]:
        """安全地计算梯度，带错误处理"""
        try:
            # 清除现有梯度
            self.model.zero_grad(set_to_none=True)

            # 计算梯度
            gradients = self._compute_gradients_with_forward_pass(training_sample)

            # 验证梯度有效性
            if not gradients:
                return None

            # 检查梯度是否包含NaN或Inf
            for name, grad in gradients.items():
                if grad is not None and (torch.isnan(grad).any() or torch.isinf(grad).any()):
                    logger.warning(f"Invalid gradient detected for {name}")
                    return None

            return gradients

        except Exception as e:
            logger.debug(f"Safe gradient computation failed: {e}")
            return None

    def _apply_parameter_perturbation(self, vector: Dict[str, torch.Tensor],
                                    eps: float, original_params: Dict[str, torch.Tensor]):
        """应用参数扰动：param = original_param + eps * vector"""
        for name, param in self.model.named_parameters():
            if name in vector and name in original_params and param.requires_grad:
                param.data.copy_(original_params[name])
                param.data.add_(vector[name], alpha=eps)

    def _synchronize_parameters(self, vector: Dict[str, torch.Tensor], ulysses_group):
        """在Ulysses并行组中同步参数，使用超时机制避免死锁"""
        import torch.distributed as dist
        try:
            # 使用异步操作和超时机制避免死锁
            handles = []
            for name, param in self.model.named_parameters():
                if name in vector and param.requires_grad:
                    # 使用异步all_reduce避免死锁
                    handle = dist.all_reduce(param.data, group=ulysses_group, async_op=True)
                    handles.append(handle)

            # 等待所有操作完成，设置超时
            for handle in handles:
                handle.wait()

        except Exception as e:
            logger.warning(f"Parameter synchronization failed: {e}, continuing without sync")

    def _restore_parameters(self, original_params: Dict[str, torch.Tensor]):
        """恢复原始参数"""
        for name, param in self.model.named_parameters():
            if name in original_params and param.requires_grad:
                param.data.copy_(original_params[name])

    def _compute_gradients_with_distributed_sync(self, training_sample: Dict) -> Dict[str, torch.Tensor]:
        """
        计算梯度并在分布式环境下同步，使用改进的同步机制避免死锁。
        """
        try:
            from verl.utils.ulysses import get_ulysses_sequence_parallel_group, get_ulysses_sequence_parallel_world_size
            import torch.distributed as dist

            # 清除现有梯度
            self.model.zero_grad(set_to_none=True)

            # 计算梯度
            gradients = self._compute_gradients_with_forward_pass(training_sample)

            # 在Ulysses并行组中同步梯度
            ulysses_group = get_ulysses_sequence_parallel_group()
            ulysses_world_size = get_ulysses_sequence_parallel_world_size()

            if ulysses_group is not None and ulysses_world_size > 1:
                # 使用异步操作和批量处理避免死锁
                handles = []
                gradient_tensors = []
                gradient_names = []

                for name in gradients:
                    if gradients[name] is not None:
                        gradient_tensors.append(gradients[name])
                        gradient_names.append(name)

                # 批量同步梯度
                try:
                    for i, tensor in enumerate(gradient_tensors):
                        handle = dist.all_reduce(tensor, group=ulysses_group, async_op=True)
                        handles.append(handle)

                    # 等待所有操作完成
                    for handle in handles:
                        handle.wait()

                    # 平均化梯度
                    for i, name in enumerate(gradient_names):
                        gradients[name] = gradient_tensors[i] / ulysses_world_size

                except Exception as sync_e:
                    logger.warning(f"Gradient synchronization failed: {sync_e}, using local gradients")

            return gradients

        except Exception as e:
            logger.debug(f"Failed to compute gradients with distributed sync: {e}")
            # 回退到本地梯度计算
            try:
                return self._compute_gradients_with_forward_pass(training_sample)
            except:
                return {}

    def _compute_finite_difference_result(self, grad_plus: Dict[str, torch.Tensor],
                                        grad_minus: Dict[str, torch.Tensor],
                                        vector: Dict[str, torch.Tensor],
                                        eps: float, damping: float) -> Dict[str, torch.Tensor]:
        """计算有限差分结果"""
        hvp = {}
        for name in vector.keys():
            if name in grad_plus and name in grad_minus:
                grad_plus_tensor = grad_plus[name]
                grad_minus_tensor = grad_minus[name]

                # Handle potential size mismatch due to Ulysses sequence parallelism
                if grad_plus_tensor.shape != grad_minus_tensor.shape:
                    min_numel = min(grad_plus_tensor.numel(), grad_minus_tensor.numel())
                    grad_plus_flat = grad_plus_tensor.flatten()[:min_numel]
                    grad_minus_flat = grad_minus_tensor.flatten()[:min_numel]
                    diff_flat = (grad_plus_flat - grad_minus_flat) / (2 * eps)

                    # Reshape back to match vector shape
                    vec = vector[name]
                    if vec.numel() <= min_numel:
                        hvp[name] = diff_flat[:vec.numel()].view_as(vec) + damping * vec
                    else:
                        result_tensor = damping * vec
                        result_tensor.flatten()[:min_numel] = diff_flat
                        hvp[name] = result_tensor
                else:
                    hvp[name] = (grad_plus_tensor - grad_minus_tensor) / (2 * eps) + damping * vector[name]
            else:
                hvp[name] = damping * vector[name]

        return hvp

    def _diagonal_approximation_hvp(self, vector: Dict[str, torch.Tensor],
                                loss: torch.Tensor, damping: float) -> Dict[str, torch.Tensor]:
        """使用对角近似方法，兼容FSDP状态管理"""
        try:
            # 检查FSDP状态并确保模型处于正确状态
            self._ensure_fsdp_idle_state()

            # 使用no_grad上下文避免FSDP状态冲突
            with torch.no_grad():
                # 清除现有梯度
                self.model.zero_grad(set_to_none=True)

            # 重新计算梯度，但不使用retain_graph避免FSDP状态问题
            try:
                loss.backward()
            except RuntimeError as e:
                if "FSDP" in str(e) or "training state" in str(e):
                    logger.warning(f"FSDP state error during backward: {e}, using identity approximation")
                    return self._identity_approximation_hvp(vector, damping)
                else:
                    raise

            # 使用梯度的平方作为Hessian对角线的近似
            hvp = {}
            for name, param in self.model.named_parameters():
                if name in vector and param.grad is not None:
                    grad = param.grad
                    vec = vector[name]

                    # Handle potential size mismatch due to Ulysses sequence parallelism
                    if grad.shape != vec.shape:
                        # Use the smaller tensor size to avoid out-of-bounds errors
                        min_numel = min(grad.numel(), vec.numel())
                        grad_flat = grad.flatten()[:min_numel]
                        vec_flat = vec.flatten()[:min_numel]

                        # 对角近似: H_ii ≈ g_i^2 / ||g||
                        grad_norm = torch.norm(grad_flat)
                        if grad_norm > 1e-8:
                            diag_approx = grad_flat * grad_flat / grad_norm
                            result_flat = diag_approx * vec_flat + damping * vec_flat
                        else:
                            result_flat = damping * vec_flat

                        # Reshape back to original vector shape (truncated)
                        if vec.numel() <= min_numel:
                            hvp[name] = result_flat.view_as(vec)
                        else:
                            # If vector is larger, pad with damped values
                            result_tensor = damping * vec
                            result_tensor.flatten()[:min_numel] = result_flat
                            hvp[name] = result_tensor
                    else:
                        # 对角近似: H_ii ≈ g_i^2 / ||g||
                        grad_norm = torch.norm(grad)
                        if grad_norm > 1e-8:
                            diag_approx = grad * grad / grad_norm
                            hvp[name] = diag_approx * vec + damping * vec
                        else:
                            hvp[name] = damping * vec
                elif name in vector:
                    hvp[name] = damping * vector[name]

            return hvp

        except Exception as e:
            logger.warning(f"Diagonal approximation failed: {e}")
            return self._identity_approximation_hvp(vector, damping)

    def _ensure_fsdp_idle_state(self):
        """确保FSDP模型处于IDLE状态"""
        try:
            # 检查是否是FSDP模型
            is_fsdp = any('_fsdp_wrapped_module' in name for name, _ in self.model.named_parameters())
            if not is_fsdp:
                return

            # 尝试清理FSDP状态
            with torch.no_grad():
                self.model.zero_grad(set_to_none=True)

            # 如果有FSDP相关的状态管理方法，调用它们
            if hasattr(self.model, '_reset_lazy_init'):
                try:
                    self.model._reset_lazy_init()
                except:
                    pass

        except Exception as e:
            logger.debug(f"Failed to ensure FSDP idle state: {e}")

    def _identity_approximation_hvp(self, vector: Dict[str, torch.Tensor], damping: float) -> Dict[str, torch.Tensor]:
        """最简单的身份矩阵近似"""
        hvp = {}
        for name in vector.keys():
            hvp[name] = damping * vector[name]
        return hvp

    def _compute_gradients_at_current_params(self) -> Dict[str, torch.Tensor]:
        """在当前参数下计算梯度（需要重新前向传播）"""
        # 这里需要访问当前的批次数据
        # 由于我们在HVP计算中，可能需要存储批次数据
        # 或者使用其他方法
        gradients = {}
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                gradients[name] = param.grad.clone().detach()
        return gradients

    def _compute_gradients_with_forward_pass(self, training_sample: Dict) -> Dict[str, torch.Tensor]:
        """通过前向传播重新计算梯度"""
        try:
            # 过滤模型输入
            filtered_batch = self._filter_model_inputs(training_sample)

            # 确保有标签用于损失计算
            if 'labels' not in filtered_batch:
                # 如果没有标签，尝试从input_ids生成标签
                if 'input_ids' in filtered_batch:
                    filtered_batch['labels'] = filtered_batch['input_ids'].clone()
                else:
                    logger.warning("No labels or input_ids available for gradient computation")
                    return {}

            # 前向传播
            outputs = self.model(**filtered_batch)

            # 计算损失
            if hasattr(outputs, 'loss') and outputs.loss is not None:
                loss = outputs.loss
            else:
                # 手动计算损失
                if hasattr(outputs, 'logits'):
                    logits = outputs.logits
                    labels = filtered_batch['labels']

                    # 标准的语言模型损失计算
                    shift_logits = logits[..., :-1, :].contiguous()
                    shift_labels = labels[..., 1:].contiguous()

                    # 忽略padding tokens (通常是-100)
                    loss_fct = torch.nn.CrossEntropyLoss(ignore_index=-100)
                    loss = loss_fct(shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1))
                else:
                    logger.warning("No logits available for loss computation")
                    return {}

            # 检查损失是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning("Invalid loss (NaN or Inf) for gradient computation")
                return {}

            # 计算梯度
            loss.backward()

            # 收集梯度
            gradients = {}
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    gradients[name] = param.grad.clone().detach()

            return gradients

        except Exception as e:
            logger.debug(f"Failed to compute gradients with forward pass: {e}")
            return {}
    def _restore_gradients(self, original_grads: Dict[str, torch.Tensor]):
        """
        Restore original gradient state to prevent optimizer corruption.
        """
        try:
            for name, param in self.model.named_parameters():
                if name in original_grads:
                    param.grad = original_grads[name].clone()
                else:
                    param.grad = None
        except Exception as e:
            logger.warning(f"Failed to restore gradients: {e}")
            # Force clear all gradients as fallback
            self.model.zero_grad(set_to_none=True)

    def _hessian_vector_product_standard(self,
                                    vector: Dict[str, torch.Tensor],
                                    loss: torch.Tensor,
                                    damping: float) -> Dict[str, torch.Tensor]:
        """
        Standard Hessian-vector product computation for non-FSDP models.
        """
        # First-order gradients
        first_grads = torch.autograd.grad(loss, self.model.parameters(), 
                                        create_graph=True, allow_unused=True)

        # Compute gradient-vector product
        grad_vector_product = torch.tensor(0.0, device=self.device, requires_grad=True)
        has_valid_grads = False
        
        for grad, (name, param) in zip(first_grads, self.model.named_parameters()):
            if grad is not None and name in vector:
                if not param.requires_grad:
                    continue
                grad_vector_product = grad_vector_product + torch.sum(grad * vector[name])
                has_valid_grads = True

        # Check if we have any valid gradients
        if not has_valid_grads:
            logger.warning("No valid gradients found for standard model")
            hvp = {}
            for name in vector.keys():
                hvp[name] = damping * vector[name]
            return hvp

        # Second-order gradients (Hessian-vector product)
        try:
            hvp_grads = torch.autograd.grad(grad_vector_product, self.model.parameters(), 
                                        retain_graph=False)

            # Add damping term: (H + λI) * v = H * v + λ * v
            hvp = {}
            for (name, _), hvp_grad in zip(self.model.named_parameters(), hvp_grads):
                if name in vector:
                    hvp[name] = hvp_grad + damping * vector[name]

            return hvp

        except RuntimeError as e:
            if 'derivative' in str(e) and 'not implemented' in str(e):
                logger.warning(f'Second-order derivatives not available: {e}. Using identity approximation.')
                hvp = {}
                for name in vector.keys():
                    hvp[name] = damping * vector[name]
                return hvp
            else:
                raise

    def _clear_memory(self):
        """
        Clear memory following FSDP patterns and ensure gradient consistency.
        """
        # Clear gradients with device/dtype consistency check
        with torch.no_grad():
            self.model.zero_grad(set_to_none=True)

        # Additional cleanup: ensure all parameters are in consistent state
        self._ensure_model_consistency()

        # Clear CUDA cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # Force garbage collection
        gc.collect()

    def _ensure_model_consistency(self):
        """Ensure model parameters are in consistent device/dtype state after influence computation."""
        try:
            with torch.no_grad():
                for _, param in self.model.named_parameters():
                    # Ensure parameter is on the expected device (CUDA if available)
                    if torch.cuda.is_available() and param.device.type != 'cuda':
                        param.data = param.data.cuda()

                    # Clear any lingering gradients that might have inconsistent device/dtype
                    if param.grad is not None:
                        param.grad = None
        except Exception as e:
            logger.warning(f"Failed to ensure model consistency: {e}")
            # If consistency check fails, at least clear gradients
            self.model.zero_grad(set_to_none=True)

    def _compute_influence_score_fallback(self,
                                        training_sample: Dict,
                                        validation_batch: Dict,  # 保留参数以保持接口一致性
                                        loss_fn: Callable) -> float:
        """
        Fallback influence score computation with minimal memory usage.
        """
        try:
            # Simple approximation: use gradient dot product as influence score
            self._clear_memory()

            # Compute training gradients
            training_grads = self._compute_gradients_with_memory_management(training_sample, loss_fn)

            # Compute simple influence approximation
            influence_score = 0.0
            for name in self._validation_gradients.keys():
                if name in training_grads:
                    val_grad = self._validation_gradients[name]
                    train_grad = training_grads[name]

                    # Handle potential size mismatch due to Ulysses sequence parallelism
                    if val_grad.shape != train_grad.shape:
                        # Use the smaller tensor size to avoid out-of-bounds errors
                        min_numel = min(val_grad.numel(), train_grad.numel())
                        val_grad_flat = val_grad.flatten()[:min_numel]
                        train_grad_flat = train_grad.flatten()[:min_numel]
                        influence_score -= torch.sum(val_grad_flat * train_grad_flat).item()
                        continue

                    influence_score -= torch.sum(val_grad * train_grad).item()

            return influence_score / self.regularization_lambda  # Scale by regularization

        except Exception as e:
            logger.warning(f"Fallback influence computation failed: {e}")
            return 0.0
        finally:
            self._clear_memory()

    def extract_kfac_factors(self, batch: Dict, loss_fn: Callable):
        """
        Backward compatibility method for K-FAC factor extraction.

        In the CG implementation, this is a no-op since we don't use K-FAC factors.
        The method exists for compatibility with existing test code.

        Args:
            batch: Input batch (unused in CG implementation)
            loss_fn: Loss function (unused in CG implementation)
        """
        logger.info("extract_kfac_factors called - no-op in CG implementation")
        # Set empty factors for compatibility
        self.kfac_factors = {}


class DistributedInfluenceFunctionCalculator:
    """
    Distributed influence function calculator that works with Ray worker groups.

    This calculator coordinates influence function computation across distributed workers
    without requiring direct access to the model on the driver process.
    """

    def __init__(self,
                 worker_group,
                 config: Optional[Dict] = None):
        """
        Initialize distributed influence function calculator.

        Args:
            worker_group: Ray worker group containing the distributed model
            config: Configuration dictionary with influence function parameters
        """
        self.worker_group = worker_group
        self.config = config or {}

        # Initialize influence function computation on workers
        self.worker_group.init_influence_functions(self.config)

        # Cache for validation gradients
        self._validation_gradients_cached = False

    def compute_validation_gradients(self, validation_data_proto):
        """
        Compute and cache validation gradients on distributed workers.

        Args:
            validation_data_proto: Validation data in DataProto format
        """
        # Send validation batch to workers and compute gradients
        self.worker_group.compute_validation_gradients(validation_data_proto)
        self._validation_gradients_cached = True

    def compute_influence_scores(self, training_data_proto):
        """
        Compute influence scores for training samples using distributed workers.

        Args:
            training_data_proto: Training data in DataProto format

        Returns:
            DataProto containing influence scores
        """
        if not self._validation_gradients_cached:
            raise ValueError("Validation gradients must be computed first")

        # Send training samples to workers and compute influence scores
        influence_output = self.worker_group.compute_influence_scores(training_data_proto)

        return influence_output

    def _aggregate_influence_scores(self, distributed_scores):
        """
        Aggregate influence scores from distributed workers.

        Args:
            distributed_scores: Scores from different workers

        Returns:
            Aggregated influence scores
        """
        # Simple aggregation - in practice, you might want more sophisticated methods
        if isinstance(distributed_scores, list) and len(distributed_scores) > 0:
            if isinstance(distributed_scores[0], list):
                # Flatten nested lists from multiple workers
                return [score for worker_scores in distributed_scores for score in worker_scores]
            else:
                return distributed_scores
        return []


def create_influence_calculator(model: nn.Module = None,
                              config: Optional[Dict] = None,
                              worker_group = None):
    """
    Factory function to create an influence function calculator.

    Args:
        model: Neural network model (for single-node computation)
        config: Configuration dictionary with influence function parameters
        worker_group: Ray worker group (for distributed computation)

    Returns:
        InfluenceFunctionCalculator or DistributedInfluenceFunctionCalculator instance
    """
    if config is None:
        config = {}

    # If worker_group is provided, use distributed calculator
    if worker_group is not None:
        return DistributedInfluenceFunctionCalculator(worker_group, config)

    # Otherwise, use single-node calculator with CG method
    if model is None:
        raise ValueError("Either model or worker_group must be provided")

    # Parameters for CG-based influence function calculator
    calculator_params = {
        'model': model,
        'regularization_lambda': config.get('regularization_lambda', 1e-3),
        'damping_factor': config.get('damping_factor', 1e-3),
        'cg_max_iterations': config.get('cg_max_iterations', 100),
        'cg_tolerance': config.get('cg_tolerance', 1e-6),
        'max_samples_per_batch': config.get('max_samples_per_batch', 32),
        'freeze_attention_layers': config.get('freeze_attention_layers', False)
    }

    return InfluenceFunctionCalculator(**calculator_params)


# For backward compatibility, create an alias
KFACInfluenceFunctionCalculator = InfluenceFunctionCalculator
