# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Qwen3 configuration and model registration for transformers compatibility.
This module provides backward compatibility for transformers versions that don't support Qwen3.
"""

import torch
from transformers import Qwen2Config, Qwen2ForCausalLM
from transformers.models.auto import CONFIG_MAPPING, MODEL_FOR_CAUSAL_LM_MAPPING
from transformers.configuration_utils import PretrainedConfig
from typing import Dict, Any, Optional


class Qwen3Config(Qwen2Config):
    """
    Qwen3 configuration class that extends Qwen2Config with Qwen3-specific fields.
    
    This class provides compatibility for Qwen3 models by extending Qwen2Config
    and adding Qwen3-specific configuration fields like head_dim and attention_bias.
    """
    
    model_type = "qwen3"
    
    def __init__(
        self,
        vocab_size=151936,
        hidden_size=1024,  # Default to Qwen3-0.6B size
        intermediate_size=3072,  # Default to Qwen3-0.6B size
        num_hidden_layers=28,  # Default to Qwen3-0.6B size
        num_attention_heads=16,  # Default to Qwen3-0.6B size
        num_key_value_heads=8,
        head_dim=128,  # Qwen3 specific default
        attention_bias=False,  # Qwen3 specific default
        hidden_act="silu",
        max_position_embeddings=40960,
        initializer_range=0.02,
        rms_norm_eps=1e-6,
        use_cache=True,
        tie_word_embeddings=True,  # Qwen3 default
        rope_theta=1000000.0,
        rope_scaling=None,
        sliding_window=None,
        max_window_layers=28,  # Qwen3 specific
        use_sliding_window=False,
        attention_dropout=0.0,
        **kwargs
    ):
        # Set head_dim if not provided
        if head_dim is None:
            head_dim = hidden_size // num_attention_heads
        
        # Initialize parent class
        super().__init__(
            vocab_size=vocab_size,
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            num_hidden_layers=num_hidden_layers,
            num_attention_heads=num_attention_heads,
            num_key_value_heads=num_key_value_heads,
            hidden_act=hidden_act,
            max_position_embeddings=max_position_embeddings,
            initializer_range=initializer_range,
            rms_norm_eps=rms_norm_eps,
            use_cache=use_cache,
            tie_word_embeddings=tie_word_embeddings,
            rope_theta=rope_theta,
            rope_scaling=rope_scaling,
            sliding_window=sliding_window,
            max_window_layers=max_window_layers,
            use_sliding_window=use_sliding_window,
            attention_dropout=attention_dropout,
            **kwargs
        )
        
        # Qwen3-specific fields
        self.head_dim = head_dim
        self.attention_bias = attention_bias


class Qwen3Attention(torch.nn.Module):
    """
    Qwen3 attention layer that correctly handles head_dim from config.

    This is necessary because Qwen2Attention doesn't check for head_dim in config
    and always calculates it as hidden_size // num_heads, which is incorrect for Qwen3.
    """

    def __init__(self, config: Qwen3Config, layer_idx: Optional[int] = None):
        super().__init__()
        self.config = config
        self.layer_idx = layer_idx

        self.hidden_size = config.hidden_size
        self.num_heads = config.num_attention_heads

        # Qwen3 specific: use head_dim from config if available
        if hasattr(config, 'head_dim') and config.head_dim is not None:
            self.head_dim = config.head_dim
        else:
            self.head_dim = self.hidden_size // self.num_heads

        self.num_key_value_heads = config.num_key_value_heads
        self.num_key_value_groups = self.num_heads // self.num_key_value_heads
        self.max_position_embeddings = config.max_position_embeddings
        self.rope_theta = config.rope_theta
        self.is_causal = True
        self.attention_dropout = config.attention_dropout

        # Use attention_bias from config if available
        attention_bias = getattr(config, 'attention_bias', True)

        # Create projection layers with correct dimensions
        self.q_proj = torch.nn.Linear(self.hidden_size, self.num_heads * self.head_dim, bias=attention_bias)
        self.k_proj = torch.nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=attention_bias)
        self.v_proj = torch.nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=attention_bias)
        self.o_proj = torch.nn.Linear(self.num_heads * self.head_dim, self.hidden_size, bias=False)

        # Import here to avoid circular imports
        from transformers.models.qwen2.modeling_qwen2 import Qwen2RotaryEmbedding
        self.rotary_emb = Qwen2RotaryEmbedding(config=self.config)

    def forward(self, *args, **kwargs):
        # Import the original Qwen2Attention forward method and use it
        from transformers.models.qwen2.modeling_qwen2 import Qwen2Attention
        # Use the same forward logic as Qwen2Attention
        return Qwen2Attention.forward(self, *args, **kwargs)


class Qwen3ForCausalLM(Qwen2ForCausalLM):
    """
    Qwen3 causal language model that extends Qwen2ForCausalLM.

    This class provides compatibility for Qwen3 models by using the same
    architecture as Qwen2 but with Qwen3-specific configuration and attention layers.
    """

    config_class = Qwen3Config

    def __init__(self, config):
        super().__init__(config)

        # Replace all attention layers with Qwen3Attention
        for i, layer in enumerate(self.model.layers):
            layer.self_attn = Qwen3Attention(config, layer_idx=i)


def register_qwen3_to_transformers():
    """
    Register Qwen3 configuration and model classes to transformers auto classes.
    
    This function monkey-patches the transformers library to recognize Qwen3 models
    when the library version doesn't natively support them.
    """
    
    # Register Qwen3Config
    if "qwen3" not in CONFIG_MAPPING:
        CONFIG_MAPPING.register("qwen3", Qwen3Config)
        print("✓ Registered Qwen3Config to CONFIG_MAPPING")
    
    # Register Qwen3ForCausalLM
    if "qwen3" not in MODEL_FOR_CAUSAL_LM_MAPPING:
        MODEL_FOR_CAUSAL_LM_MAPPING.register(Qwen3Config, Qwen3ForCausalLM)
        print("✓ Registered Qwen3ForCausalLM to MODEL_FOR_CAUSAL_LM_MAPPING")
    
    # Also register in the reverse mapping for AutoConfig
    try:
        from transformers.models.auto.configuration_auto import _CONFIG_MAPPING_NAMES
        if "qwen3" not in _CONFIG_MAPPING_NAMES:
            _CONFIG_MAPPING_NAMES["qwen3"] = "Qwen3Config"
            print("✓ Registered Qwen3Config to _CONFIG_MAPPING_NAMES")
    except ImportError:
        pass
    
    print("🎉 Qwen3 registration completed successfully!")


def patch_auto_config_from_pretrained():
    """
    Patch AutoConfig.from_pretrained to handle Qwen3 models gracefully.
    
    This function modifies the AutoConfig.from_pretrained method to automatically
    convert Qwen3 configs to Qwen2 configs when needed.
    """
    from transformers import AutoConfig
    
    # Store the original method
    original_from_pretrained = AutoConfig.from_pretrained
    
    @classmethod
    def patched_from_pretrained(cls, pretrained_model_name_or_path, **kwargs):
        try:
            # Try the original method first
            return original_from_pretrained(pretrained_model_name_or_path, **kwargs)
        except ValueError as e:
            if "model type `qwen3`" in str(e) and "does not recognize this architecture" in str(e):
                print(f"⚠️  Detected Qwen3 model, loading with Qwen3Config...")

                # Load the config as a dictionary first
                from transformers.configuration_utils import PretrainedConfig
                config_dict, kwargs_updated = PretrainedConfig.get_config_dict(pretrained_model_name_or_path, **kwargs)

                # Create Qwen3Config with the original config dict
                # This preserves all the original parameters from the model
                return Qwen3Config.from_dict(config_dict, **kwargs_updated)
            else:
                # Re-raise other errors
                raise e


def patch_qwen3_for_vllm_compatibility():
    """
    Patch Qwen3Config to appear as Qwen2 for vLLM compatibility.

    vLLM doesn't support Qwen3ForCausalLM yet, so we need to make Qwen3 models
    appear as Qwen2 models to vLLM while preserving the correct Qwen3 configuration.
    """

    # Store the original to_dict method
    original_to_dict = Qwen3Config.to_dict

    def patched_to_dict(self):
        """Return a dict that makes Qwen3 appear as Qwen2 for vLLM compatibility"""
        config_dict = original_to_dict(self)

        # Check if this is being called by vLLM (improved heuristic check)
        import inspect
        frame = inspect.currentframe()
        is_vllm_context = False

        try:
            # Look through the call stack for vLLM-related modules
            for i in range(15):  # Check up to 15 frames up
                frame = frame.f_back
                if frame is None:
                    break

                filename = frame.f_code.co_filename.lower()
                function_name = frame.f_code.co_name.lower()

                # Check for vLLM-related patterns
                if ('vllm' in filename or
                    'model_config' in function_name or
                    'modelconfig' in function_name or
                    'llm_engine' in filename or
                    'engine_args' in filename):
                    is_vllm_context = True
                    break
        except:
            pass

        # Also check for specific vLLM environment variables or imports
        if not is_vllm_context:
            try:
                import sys
                # Check if vLLM modules are loaded
                vllm_modules = [name for name in sys.modules.keys() if 'vllm' in name.lower()]
                if vllm_modules:
                    # Additional check: see if we're in a vLLM-related call
                    frame = inspect.currentframe()
                    for i in range(10):
                        frame = frame.f_back
                        if frame is None:
                            break
                        if any(vllm_mod in frame.f_code.co_filename for vllm_mod in ['config.py', 'registry.py']):
                            is_vllm_context = True
                            break
            except:
                pass

        # If this is being called by vLLM, convert to Qwen2 for compatibility
        if is_vllm_context:
            print("🔄 Converting Qwen3 config to Qwen2 for vLLM compatibility...")
            config_dict = config_dict.copy()
            config_dict["model_type"] = "qwen2"

            # Convert architectures
            if "architectures" in config_dict:
                architectures = config_dict["architectures"]
                if isinstance(architectures, list):
                    config_dict["architectures"] = [
                        arch.replace("Qwen3", "Qwen2") for arch in architectures
                    ]

        return config_dict

    # Apply the patch
    Qwen3Config.to_dict = patched_to_dict
    print("✓ Patched Qwen3Config.to_dict for vLLM compatibility")


def initialize_qwen3_support():
    """
    Initialize Qwen3 support in transformers.

    This function should be called early in the application to ensure
    Qwen3 models can be loaded properly.
    """
    print("🔧 Initializing Qwen3 support...")

    # Register Qwen3 classes
    register_qwen3_to_transformers()

    # Patch AutoConfig for backward compatibility
    patch_auto_config_from_pretrained()

    # Patch Qwen3Config for vLLM compatibility
    patch_qwen3_for_vllm_compatibility()

    print("✅ Qwen3 support initialized successfully!")


# Auto-initialize when this module is imported
initialize_qwen3_support()
