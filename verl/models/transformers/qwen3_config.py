# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Qwen3 configuration and model registration for transformers compatibility.
This module provides backward compatibility for transformers versions that don't support Qwen3.
"""

from transformers import Qwen2Config, Qwen2ForCausalLM
from transformers.models.auto import CONFIG_MAPPING, MODEL_FOR_CAUSAL_LM_MAPPING
from transformers.configuration_utils import PretrainedConfig
from typing import Dict, Any


class Qwen3Config(Qwen2Config):
    """
    Qwen3 configuration class that extends Qwen2Config with Qwen3-specific fields.
    
    This class provides compatibility for Qwen3 models by extending Qwen2Config
    and adding Qwen3-specific configuration fields like head_dim and attention_bias.
    """
    
    model_type = "qwen3"
    
    def __init__(
        self,
        vocab_size=151936,
        hidden_size=4096,
        intermediate_size=12288,
        num_hidden_layers=36,
        num_attention_heads=32,
        num_key_value_heads=8,
        head_dim=None,
        attention_bias=False,
        hidden_act="silu",
        max_position_embeddings=40960,
        initializer_range=0.02,
        rms_norm_eps=1e-6,
        use_cache=True,
        tie_word_embeddings=False,
        rope_theta=1000000.0,
        rope_scaling=None,
        sliding_window=None,
        max_window_layers=None,
        use_sliding_window=False,
        attention_dropout=0.0,
        **kwargs
    ):
        # Set head_dim if not provided
        if head_dim is None:
            head_dim = hidden_size // num_attention_heads
        
        # Initialize parent class
        super().__init__(
            vocab_size=vocab_size,
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            num_hidden_layers=num_hidden_layers,
            num_attention_heads=num_attention_heads,
            num_key_value_heads=num_key_value_heads,
            hidden_act=hidden_act,
            max_position_embeddings=max_position_embeddings,
            initializer_range=initializer_range,
            rms_norm_eps=rms_norm_eps,
            use_cache=use_cache,
            tie_word_embeddings=tie_word_embeddings,
            rope_theta=rope_theta,
            rope_scaling=rope_scaling,
            sliding_window=sliding_window,
            max_window_layers=max_window_layers,
            use_sliding_window=use_sliding_window,
            attention_dropout=attention_dropout,
            **kwargs
        )
        
        # Qwen3-specific fields
        self.head_dim = head_dim
        self.attention_bias = attention_bias


class Qwen3ForCausalLM(Qwen2ForCausalLM):
    """
    Qwen3 causal language model that extends Qwen2ForCausalLM.
    
    This class provides compatibility for Qwen3 models by using the same
    architecture as Qwen2 but with Qwen3-specific configuration.
    """
    
    config_class = Qwen3Config
    
    def __init__(self, config):
        super().__init__(config)


def register_qwen3_to_transformers():
    """
    Register Qwen3 configuration and model classes to transformers auto classes.
    
    This function monkey-patches the transformers library to recognize Qwen3 models
    when the library version doesn't natively support them.
    """
    
    # Register Qwen3Config
    if "qwen3" not in CONFIG_MAPPING:
        CONFIG_MAPPING.register("qwen3", Qwen3Config)
        print("✓ Registered Qwen3Config to CONFIG_MAPPING")
    
    # Register Qwen3ForCausalLM
    if "qwen3" not in MODEL_FOR_CAUSAL_LM_MAPPING:
        MODEL_FOR_CAUSAL_LM_MAPPING.register(Qwen3Config, Qwen3ForCausalLM)
        print("✓ Registered Qwen3ForCausalLM to MODEL_FOR_CAUSAL_LM_MAPPING")
    
    # Also register in the reverse mapping for AutoConfig
    try:
        from transformers.models.auto.configuration_auto import _CONFIG_MAPPING_NAMES
        if "qwen3" not in _CONFIG_MAPPING_NAMES:
            _CONFIG_MAPPING_NAMES["qwen3"] = "Qwen3Config"
            print("✓ Registered Qwen3Config to _CONFIG_MAPPING_NAMES")
    except ImportError:
        pass
    
    print("🎉 Qwen3 registration completed successfully!")


def patch_auto_config_from_pretrained():
    """
    Patch AutoConfig.from_pretrained to handle Qwen3 models gracefully.
    
    This function modifies the AutoConfig.from_pretrained method to automatically
    convert Qwen3 configs to Qwen2 configs when needed.
    """
    from transformers import AutoConfig
    
    # Store the original method
    original_from_pretrained = AutoConfig.from_pretrained
    
    @classmethod
    def patched_from_pretrained(cls, pretrained_model_name_or_path, **kwargs):
        try:
            # Try the original method first
            return original_from_pretrained(pretrained_model_name_or_path, **kwargs)
        except ValueError as e:
            if "model type `qwen3`" in str(e) and "does not recognize this architecture" in str(e):
                print(f"⚠️  Detected Qwen3 model, converting to Qwen2 config for compatibility...")
                
                # Load the config as a dictionary first
                from transformers.configuration_utils import PretrainedConfig
                config_dict, kwargs = PretrainedConfig.get_config_dict(pretrained_model_name_or_path, **kwargs)
                
                # Convert qwen3 to qwen2 in the config
                if config_dict.get("model_type") == "qwen3":
                    config_dict["model_type"] = "qwen2"
                    
                    # Handle architectures field
                    if "architectures" in config_dict:
                        architectures = config_dict["architectures"]
                        if isinstance(architectures, list):
                            config_dict["architectures"] = [
                                arch.replace("Qwen3", "Qwen2") for arch in architectures
                            ]
                
                # Create Qwen2Config with the modified config
                from transformers import Qwen2Config
                return Qwen2Config.from_dict(config_dict, **kwargs)
            else:
                # Re-raise other errors
                raise e
    
    # Replace the method
    AutoConfig.from_pretrained = patched_from_pretrained
    print("✓ Patched AutoConfig.from_pretrained for Qwen3 compatibility")


def initialize_qwen3_support():
    """
    Initialize Qwen3 support in transformers.
    
    This function should be called early in the application to ensure
    Qwen3 models can be loaded properly.
    """
    print("🔧 Initializing Qwen3 support...")
    
    # Register Qwen3 classes
    register_qwen3_to_transformers()
    
    # Patch AutoConfig for backward compatibility
    patch_auto_config_from_pretrained()
    
    print("✅ Qwen3 support initialized successfully!")


# Auto-initialize when this module is imported
initialize_qwen3_support()
