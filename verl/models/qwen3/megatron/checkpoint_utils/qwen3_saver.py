# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import torch
import time
from typing import Dict, Any, Optional
import torch.distributed as dist


def save_megatron_qwen3_to_state_dict(wrapped_models, config, is_value_model=False):
    """Save sharded Megatron module to merged state_dict.
    
    This function is adapted from Qwen2 saver to support Qwen3 models.
    """
    import megatron
    from megatron.core import mpu
    from megatron.utils import print_rank_0, unwrap_model
    from megatron.core.transformer.module import Float16Module
    from megatron.core import DistributedDataParallel as LocalDDP
    from torch.nn.parallel import DistributedDataParallel as torchDDP

    start_time = time.time()

    def _get_gpt_model(model):
        return model

    dp_rank = mpu.get_data_parallel_rank()
    pp_rank = mpu.get_pipeline_model_parallel_rank()
    pp_size = mpu.get_pipeline_model_parallel_world_size()
    tp_rank = mpu.get_tensor_model_parallel_rank()
    tp_size = mpu.get_tensor_model_parallel_world_size()
    virtual_pp_size = mpu.get_virtual_pipeline_model_parallel_world_size() or 1
    mp_group = mpu.get_model_parallel_group()

    if not isinstance(wrapped_models, (list, tuple)):
        wrapped_models = [wrapped_models]

    assert len(wrapped_models) == virtual_pp_size
    num_layers_per_model = config.num_hidden_layers // pp_size // virtual_pp_size
    assert num_layers_per_model * pp_size * virtual_pp_size == config.num_hidden_layers

    models = [None] * len(wrapped_models)
    for i, wrapped_model in enumerate(wrapped_models):
        models[i] = unwrap_model(wrapped_model, (torchDDP, LocalDDP, Float16Module))

    state_dict = {}

    def _gather_tensor(tensor, name) -> torch.Tensor:
        """Gather tensor from all mp ranks to rank 0"""
        if mp_group.rank() == 0:
            gathered_tensor = tensor.clone()
        else:
            gathered_tensor = None
        
        if mp_group.size() > 1:
            tensor_list = [torch.empty_like(tensor) for _ in range(mp_group.size())]
            torch.distributed.all_gather(tensor_list, tensor, group=mp_group)
            if mp_group.rank() == 0:
                gathered_tensor = tensor_list[0]
        
        return gathered_tensor

    def _gather_tp_shard_tensor(tensor, name, chunk_dim=0) -> torch.Tensor:
        """Gather tensor sharded across tp to rank 0"""
        if mp_group.rank() == 0:
            if tp_size == 1:
                return tensor.clone()
            
            # Gather from all tp ranks
            tensor_list = [torch.empty_like(tensor) for _ in range(tp_size)]
            torch.distributed.all_gather(tensor_list, tensor, group=mpu.get_tensor_model_parallel_group())
            
            # Concatenate along the chunk dimension
            gathered_tensor = torch.cat(tensor_list, dim=chunk_dim)
            return gathered_tensor
        else:
            return None

    def _gather_tp_shard_tensor_qkv(q_tensor, k_tensor, v_tensor, layer_name) -> tuple:
        """Gather qkv tensors sharded across tp to rank 0"""
        if mp_group.rank() == 0:
            if tp_size == 1:
                return q_tensor.clone(), k_tensor.clone(), v_tensor.clone()
            
            # The qkv_proj weight contains concatenated q, k, v weights
            # We need to split and gather them separately
            qkv_tensor = torch.cat([q_tensor, k_tensor, v_tensor], dim=0)
            
            # Gather from all tp ranks
            tensor_list = [torch.empty_like(qkv_tensor) for _ in range(tp_size)]
            torch.distributed.all_gather(tensor_list, qkv_tensor, group=mpu.get_tensor_model_parallel_group())
            
            # Concatenate and split back to q, k, v
            full_qkv = torch.cat(tensor_list, dim=0)
            
            # Calculate split sizes
            q_size = q_tensor.size(0) * tp_size
            k_size = k_tensor.size(0) * tp_size
            v_size = v_tensor.size(0) * tp_size
            
            q_full, k_full, v_full = torch.split(full_qkv, [q_size, k_size, v_size], dim=0)
            return q_full, k_full, v_full
        else:
            return None, None, None

    # Only rank 0 collects the state dict
    if dp_rank == 0 and mp_group.rank() == 0:
        # Embeddings
        if pp_rank == 0:
            gpt_model_module = _get_gpt_model(models[0])
            embed_weight = _gather_tp_shard_tensor(
                gpt_model_module.model.embed_tokens.weight,
                "model.embed_tokens.weight",
                chunk_dim=0
            )
            if embed_weight is not None:
                state_dict["model.embed_tokens.weight"] = embed_weight

        # Transformer layers
        layer_offset = pp_rank * num_layers_per_model * virtual_pp_size
        for virtual_pp_rank in range(virtual_pp_size):
            gpt_model_module = _get_gpt_model(models[virtual_pp_rank])
            for layer_idx in range(num_layers_per_model):
                global_layer_idx = layer_offset + virtual_pp_rank * num_layers_per_model + layer_idx
                layer_name = f"model.layers.{global_layer_idx}"
                sync_layer = gpt_model_module.model.layers[layer_idx]

                # Input layernorm
                norm_weight = _gather_tensor(sync_layer.input_layernorm.weight, f"{layer_name}.input_layernorm.weight")
                if norm_weight is not None:
                    state_dict[f"{layer_name}.input_layernorm.weight"] = norm_weight

                # Attention QKV
                q_weight, k_weight, v_weight = _gather_tp_shard_tensor_qkv(
                    sync_layer.self_attn.qkv_proj.q_weight,
                    sync_layer.self_attn.qkv_proj.k_weight,
                    sync_layer.self_attn.qkv_proj.v_weight,
                    layer_name
                )
                if q_weight is not None:
                    state_dict[f"{layer_name}.self_attn.q_proj.weight"] = q_weight
                    state_dict[f"{layer_name}.self_attn.k_proj.weight"] = k_weight
                    state_dict[f"{layer_name}.self_attn.v_proj.weight"] = v_weight

                # Attention output projection
                o_weight = _gather_tp_shard_tensor(
                    sync_layer.self_attn.o_proj.weight,
                    f"{layer_name}.self_attn.o_proj.weight",
                    chunk_dim=1
                )
                if o_weight is not None:
                    state_dict[f"{layer_name}.self_attn.o_proj.weight"] = o_weight

                # Post attention layernorm
                post_norm_weight = _gather_tensor(
                    sync_layer.post_attention_layernorm.weight,
                    f"{layer_name}.post_attention_layernorm.weight"
                )
                if post_norm_weight is not None:
                    state_dict[f"{layer_name}.post_attention_layernorm.weight"] = post_norm_weight

                # MLP gate and up projections
                gate_up_weight = _gather_tp_shard_tensor(
                    sync_layer.mlp.gate_up_proj.weight,
                    f"{layer_name}.mlp.gate_proj.weight",
                    chunk_dim=0
                )
                if gate_up_weight is not None:
                    # Split gate_up_proj back to gate_proj and up_proj
                    gate_size = gate_up_weight.size(0) // 2
                    gate_weight, up_weight = torch.split(gate_up_weight, gate_size, dim=0)
                    state_dict[f"{layer_name}.mlp.gate_proj.weight"] = gate_weight
                    state_dict[f"{layer_name}.mlp.up_proj.weight"] = up_weight

                # MLP down projection
                down_weight = _gather_tp_shard_tensor(
                    sync_layer.mlp.down_proj.weight,
                    f"{layer_name}.mlp.down_proj.weight",
                    chunk_dim=1
                )
                if down_weight is not None:
                    state_dict[f"{layer_name}.mlp.down_proj.weight"] = down_weight

        # Final layernorm
        if pp_rank + 1 == pp_size:
            gpt_model_module = _get_gpt_model(models[-1])
            final_norm_weight = _gather_tensor(gpt_model_module.model.norm.weight, "model.norm.weight")
            if final_norm_weight is not None:
                state_dict["model.norm.weight"] = final_norm_weight

            # LM head
            if is_value_model:
                lm_head_weight = _gather_tensor(gpt_model_module.lm_head.weight, "lm_head.weight")
            else:
                lm_head_weight = _gather_tp_shard_tensor(
                    gpt_model_module.lm_head.weight,
                    "lm_head.weight",
                    chunk_dim=0
                )
            if lm_head_weight is not None:
                state_dict["lm_head.weight"] = lm_head_weight

    print_rank_0(f"Saving checkpoint took {time.time() - start_time:.2f} seconds")
    return state_dict
