# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import torch
import time
from typing import Dict, Any, Callable, Optional
import torch.distributed as dist


def _megatron_calc_layer_map(config):
    """Calculate the mapping of global layer_idx to local layer_idx
    Returns:
        layer_map (Dict: int -> tuple(int, int, int)):
            mapping from the global layer index to
            a tuple of (pp_rank, virtual_pp_rank, layer_idx inside model)
    """
    import megatron
    from megatron.core import mpu

    pp_size = mpu.get_pipeline_model_parallel_world_size()
    virtual_pp_size = mpu.get_virtual_pipeline_model_parallel_world_size() or 1

    layer_map = dict()
    num_layers_per_model = config.num_hidden_layers // pp_size // virtual_pp_size
    assert num_layers_per_model * pp_size * virtual_pp_size == config.num_hidden_layers

    for pp_rank_idx in range(pp_size):
        for virtual_pp_rank_idx in range(virtual_pp_size):
            layer_offset = (virtual_pp_rank_idx * (config.num_hidden_layers // virtual_pp_size) +
                            pp_rank_idx * num_layers_per_model)
            for layer_idx in range(num_layers_per_model):
                layer_map[layer_offset + layer_idx] = (
                    pp_rank_idx,
                    virtual_pp_rank_idx,
                    layer_idx,
                )
    return layer_map


def load_state_dict_to_megatron_qwen3(state_dict, wrapped_models, config, params_dtype, is_value_model=False):
    """Load merged state_dict to sharded Megatron module in training.
    
    This function is adapted from Qwen2 loader to support Qwen3 models.
    The main differences are:
    1. Model type checking for "qwen3" instead of "qwen2"
    2. Support for Qwen3-specific configuration fields like head_dim and attention_bias
    """
    import megatron
    from megatron.core import mpu
    from megatron.utils import print_rank_0, unwrap_model
    from megatron.core.transformer.module import Float16Module
    from megatron.core import DistributedDataParallel as LocalDDP
    from torch.nn.parallel import DistributedDataParallel as torchDDP

    start_time = time.time()

    def _get_gpt_model(model):
        return model

    def broadcast_params(module):
        for param in module.parameters():
            torch.distributed.broadcast(param.data,
                                        src=mpu.get_data_parallel_src_rank(),
                                        group=mpu.get_data_parallel_group())

    dp_rank = mpu.get_data_parallel_rank()
    pp_rank = mpu.get_pipeline_model_parallel_rank()
    pp_size = mpu.get_pipeline_model_parallel_world_size()
    virtual_pp_size = mpu.get_virtual_pipeline_model_parallel_world_size() or 1
    mp_group = mpu.get_model_parallel_group()

    if torch.distributed.get_rank() == 0:
        assert mp_group.rank() == 0, f"mp_rank:[{mp_group.rank}] != 0 on rank #0"
        assert pp_rank == 0, f"pp_rank:[{pp_rank}] != 0 on rank #0"
        assert dp_rank == 0, f"dp_rank:[{dp_rank}] != 0 on rank #0"

    if not isinstance(wrapped_models, (list, tuple)):
        wrapped_models = list(wrapped_models)

    assert len(wrapped_models) == virtual_pp_size
    num_layers_per_model = config.num_hidden_layers // pp_size // virtual_pp_size
    assert num_layers_per_model * pp_size * virtual_pp_size == config.num_hidden_layers

    models = [None] * len(wrapped_models)

    for i, wrapped_model in enumerate(wrapped_models):
        models[i] = unwrap_model(wrapped_model, (torchDDP, LocalDDP, Float16Module))
        gpt_model_module = _get_gpt_model(models[i])
        assert len(gpt_model_module.model.layers) == num_layers_per_model

    def _broadcast_tensor(tensor, name) -> torch.Tensor:
        """broadcast tensor from rank0 across mp_group"""
        nonlocal state_dict
        nonlocal mp_group
        nonlocal params_dtype

        if mp_group.rank() == 0:
            if name not in state_dict:
                print_rank_0(f"Warning: {name} not found in state_dict")
                return None
            tensor_to_broadcast = state_dict[name].to(params_dtype)
        else:
            tensor_to_broadcast = torch.empty_like(tensor, dtype=params_dtype)

        torch.distributed.broadcast(tensor_to_broadcast, src=0, group=mp_group)
        return tensor_to_broadcast

    def _broadcast_tp_shard_tensor(tensor, name, chunk_dim=0) -> torch.Tensor:
        """broadcast tensor from rank0 and shard across tp"""
        nonlocal state_dict
        nonlocal mp_group
        nonlocal params_dtype

        tp_rank = mpu.get_tensor_model_parallel_rank()
        tp_size = mpu.get_tensor_model_parallel_world_size()

        if mp_group.rank() == 0:
            if name not in state_dict:
                print_rank_0(f"Warning: {name} not found in state_dict")
                return None
            tensor_to_broadcast = state_dict[name].to(params_dtype)
            # Shard the tensor
            tensor_list = torch.chunk(tensor_to_broadcast, tp_size, dim=chunk_dim)
            tensor_to_broadcast = tensor_list[tp_rank].contiguous()
        else:
            tensor_to_broadcast = torch.empty_like(tensor, dtype=params_dtype)

        torch.distributed.broadcast(tensor_to_broadcast, src=0, group=mp_group)
        return tensor_to_broadcast

    def _broadcast_tp_shard_tensor_vocab(tensor, name) -> torch.Tensor:
        """broadcast vocab tensor from rank0 and shard across tp"""
        return _broadcast_tp_shard_tensor(tensor, name, chunk_dim=0)

    def _broadcast_tp_shard_tensor_qkv(tensor, q_name, k_name, v_name) -> torch.Tensor:
        """broadcast qkv tensor from rank0 and shard across tp"""
        nonlocal state_dict
        nonlocal mp_group
        nonlocal params_dtype

        tp_rank = mpu.get_tensor_model_parallel_rank()
        tp_size = mpu.get_tensor_model_parallel_world_size()

        if mp_group.rank() == 0:
            # Concatenate q, k, v tensors
            q_tensor = state_dict[q_name].to(params_dtype)
            k_tensor = state_dict[k_name].to(params_dtype)
            v_tensor = state_dict[v_name].to(params_dtype)
            qkv_tensor = torch.cat([q_tensor, k_tensor, v_tensor], dim=0)
            
            # Shard the concatenated tensor
            tensor_list = torch.chunk(qkv_tensor, tp_size, dim=0)
            tensor_to_broadcast = tensor_list[tp_rank].contiguous()
        else:
            tensor_to_broadcast = torch.empty_like(tensor, dtype=params_dtype)

        torch.distributed.broadcast(tensor_to_broadcast, src=0, group=mp_group)
        return tensor_to_broadcast

    # Load model weights
    if dp_rank == 0:
        # Embeddings
        # -------------------
        print_rank_0("loading embeddings...")
        gpt_model_module = _get_gpt_model(models[0])
        embed_tokens_weight = None
        if pp_rank == 0:
            embed_tokens_weight = gpt_model_module.model.embed_tokens.weight
        _broadcast_tp_shard_tensor_vocab(embed_tokens_weight, "model.embed_tokens.weight")

        # Transformer layers
        # -------------------
        layer_map = _megatron_calc_layer_map(config)

        for layer in range(config.num_hidden_layers):
            print_rank_0(f"loading layer #{layer}...")
            layer_name = f"model.layers.{layer}"
            dst_pp_rank, dst_virtual_pp_rank, dst_layer_idx = layer_map[layer]

            gpt_model_module = _get_gpt_model(models[dst_virtual_pp_rank])
            sync_layer = gpt_model_module.model.layers[dst_layer_idx]

            _broadcast_tensor(
                sync_layer.input_layernorm.weight if dst_pp_rank == pp_rank else None,
                f"{layer_name}.input_layernorm.weight",
            )

            _broadcast_tp_shard_tensor_qkv(
                sync_layer.self_attn.qkv_proj.weight if dst_pp_rank == pp_rank else None,
                f"{layer_name}.self_attn.q_proj.weight",
                f"{layer_name}.self_attn.k_proj.weight",
                f"{layer_name}.self_attn.v_proj.weight",
            )

            _broadcast_tp_shard_tensor(
                sync_layer.self_attn.o_proj.weight if dst_pp_rank == pp_rank else None,
                f"{layer_name}.self_attn.o_proj.weight",
                chunk_dim=1,
            )

            _broadcast_tensor(
                sync_layer.post_attention_layernorm.weight if dst_pp_rank == pp_rank else None,
                f"{layer_name}.post_attention_layernorm.weight",
            )

            _broadcast_tp_shard_tensor(
                sync_layer.mlp.gate_up_proj.weight if dst_pp_rank == pp_rank else None,
                f"{layer_name}.mlp.gate_proj.weight",
                chunk_dim=0,
            )

            _broadcast_tp_shard_tensor(
                sync_layer.mlp.down_proj.weight if dst_pp_rank == pp_rank else None,
                f"{layer_name}.mlp.down_proj.weight",
                chunk_dim=1,
            )
        
        # Final Layernorm
        # -------------------
        print_rank_0("loading final layernorm...")
        gpt_model_module = _get_gpt_model(models[-1])
        _broadcast_tensor(
            getattr(gpt_model_module.model.norm, "weight", None),
            "model.norm.weight",
        )

        print_rank_0("loading lm_head...")
        lm_head_weight = None
        if pp_rank + 1 == pp_size:
            if is_value_model:
                lm_head_weight = gpt_model_module.lm_head.weight
            else:
                lm_head_weight = gpt_model_module.lm_head.weight
        
        if is_value_model:
            _broadcast_tensor(lm_head_weight, "lm_head.weight")
        else:
            _broadcast_tp_shard_tensor_vocab(lm_head_weight, "lm_head.weight")

    # Broadcast parameters to all data parallel ranks
    for model in models:
        broadcast_params(model)

    print_rank_0(f"Loading checkpoint took {time.time() - start_time:.2f} seconds")
    return models
