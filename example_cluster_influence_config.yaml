# Example configuration for cluster-based influence function sampling
# This configuration demonstrates how to use the new cluster-based sampling feature
# to reduce influence function computation time while maintaining cluster representation

data:
  # Enable curriculum learning with influence functions
  enable_curriculum_learning: true
  use_influence_functions: true
  
  # Standard influence function parameters
  influence_use_kfac: true
  influence_regularization_lambda: 1e-3
  influence_damping_factor: 1e-3
  influence_max_samples_per_batch: 32
  influence_kfac_damping: 1e-3
  
  # NEW: Cluster-based sampling parameters
  # Sample every N-th sample within each cluster instead of computing for all samples
  influence_cluster_sampling_interval: 5  # Compute influence for every 5th sample per cluster
  influence_cluster_key: "data_source"     # Column name containing cluster information
  
  # Data source configuration
  data_source_key: "data_source"  # or "cluster" depending on your dataset
  
  # Training data files (should contain cluster information)
  train_files: "path/to/your/clustered_train.parquet"
  val_files: "path/to/your/clustered_val.parquet"
  test_files: "path/to/your/clustered_test.parquet"
  
  train_batch_size: 32
  max_prompt_length: 1024
  max_response_length: 512

# Other training parameters
trainer:
  total_epochs: 3
  default_local_dir: "./checkpoints"

algorithm:
  adv_estimator: "grpo"

# Model configuration
actor_rollout_ref:
  actor:
    use_dynamic_bsz: true

# Example usage scenarios:

# Scenario 1: High efficiency (80-90% reduction in computation)
# influence_cluster_sampling_interval: 10
# - Computes influence for every 10th sample in each cluster
# - Significant speedup with minimal impact on cluster representation

# Scenario 2: Balanced efficiency (50-70% reduction)
# influence_cluster_sampling_interval: 5
# - Computes influence for every 5th sample in each cluster
# - Good balance between speed and accuracy

# Scenario 3: Conservative sampling (30-50% reduction)
# influence_cluster_sampling_interval: 3
# - Computes influence for every 3rd sample in each cluster
# - More conservative approach with higher accuracy

# Scenario 4: No sampling (original behavior)
# influence_cluster_sampling_interval: 1
# - Computes influence for all samples (default behavior)
# - No efficiency gain but maximum accuracy

# Notes:
# 1. The cluster_key should match the column name in your dataset that contains cluster IDs
# 2. Common cluster keys: "data_source", "cluster", "accuracy_bin", "difficulty_level"
# 3. Ensure your dataset has been preprocessed with clustering information
# 4. The sampling is done within each cluster to maintain representation across all clusters
# 5. Samples not selected for influence computation will receive a default score of 0.0
